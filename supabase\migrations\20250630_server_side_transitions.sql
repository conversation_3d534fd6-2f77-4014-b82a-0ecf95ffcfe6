-- Add question sequence number to track question order
ALTER TABLE game_rooms 
ADD COLUMN IF NOT EXISTS question_sequence INTEGER DEFAULT 0;

-- Update existing rooms to have proper sequence numbers
UPDATE game_rooms 
SET question_sequence = COALESCE(current_round_number, 1) 
WHERE question_sequence = 0;

-- Function to auto-advance questions after 7 seconds
CREATE OR REPLACE FUNCTION auto_advance_question()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  room_record RECORD;
  next_question_data JSONB;
  player_count INTEGER;
  answer_count INTEGER;
BEGIN
  -- Find all active rooms where question has been active for > 7 seconds
  FOR room_record IN 
    SELECT 
      gr.id,
      gr.current_question_data,
      gr.question_started_at,
      gr.current_round_number,
      gr.question_sequence,
      COUNT(DISTINCT gp.user_id) as player_count,
      COUNT(DISTINCT (cra->>'userId')::text) as answer_count
    FROM game_rooms gr
    LEFT JOIN game_players gp ON gp.room_id = gr.id
    LEFT JOIN LATERAL jsonb_array_elements(
      CASE 
        WHEN jsonb_typeof(gr.current_round_answers) = 'array' 
        THEN gr.current_round_answers 
        ELSE '[]'::jsonb 
      END
    ) cra ON (cra->>'questionId')::text = (gr.current_question_data->>'questionId')::text
    WHERE gr.status = 'active'
      AND gr.current_question_data IS NOT NULL
      AND gr.question_started_at IS NOT NULL
      AND gr.question_started_at < NOW() - INTERVAL '7 seconds'
      AND gr.transition_until IS NULL -- Not already transitioning
    GROUP BY gr.id, gr.current_question_data, gr.question_started_at, 
             gr.current_round_number, gr.question_sequence
  LOOP
    -- Generate next question (simplified - in production this would call your question generation logic)
    next_question_data := jsonb_build_object(
      'questionId', gen_random_uuid()::text,
      'correctPlayerId', floor(random() * 1000 + 1)::int,
      'imageUrl', '/players_images/placeholder.jpg',
      'choices', jsonb_build_array(
        jsonb_build_object('name', 'Player A', 'isCorrect', true),
        jsonb_build_object('name', 'Player B', 'isCorrect', false),
        jsonb_build_object('name', 'Player C', 'isCorrect', false),
        jsonb_build_object('name', 'Player D', 'isCorrect', false)
      ),
      'correctChoiceName', 'Player A'
    );
    
    -- Update the room to next question
    UPDATE game_rooms
    SET 
      current_question_data = next_question_data,
      current_round_answers = '[]'::jsonb,
      current_round_number = current_round_number + 1,
      question_sequence = question_sequence + 1,
      question_started_at = NOW(),
      transition_until = NULL,
      next_question_data = NULL,
      last_activity_timestamp = NOW()
    WHERE id = room_record.id
      AND status = 'active'
      AND question_sequence = room_record.question_sequence; -- Prevent race conditions
      
    -- Log the auto-advance
    RAISE NOTICE 'Auto-advanced room % to next question. Players: %, Answers: %', 
      room_record.id, room_record.player_count, room_record.answer_count;
  END LOOP;
END;
$$;

-- Create a scheduled job to run every second (using pg_cron extension if available)
-- For now, we'll rely on the edge function calling this periodically
COMMENT ON FUNCTION auto_advance_question() IS 
  'Automatically advances questions in active game rooms after 7 seconds. 
   Should be called periodically by a scheduled job or edge function.';

-- Create an index for efficient querying of rooms needing auto-advance
CREATE INDEX IF NOT EXISTS idx_game_rooms_auto_advance 
ON game_rooms (status, question_started_at) 
WHERE status = 'active' AND question_started_at IS NOT NULL;