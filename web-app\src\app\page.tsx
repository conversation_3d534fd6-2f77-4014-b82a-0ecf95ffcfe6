'use client'; // This page now needs client-side interactivity

import { useEffect, useState, Suspense, useCallback, useRef, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { useGameStore } from '@/stores/gameStore';
import { PlayerImageDisplay } from '@/components/game/PlayerImageDisplay';
import { ChoiceButton } from '@/components/game/ChoiceButton';
import { RecentAnswersList } from '@/components/game/RecentAnswersList';
import { PlayerInfoPanel } from '@/components/game/PlayerInfoPanel';
import { But<PERSON> } from '@/components/ui/button'; // For Start/Next button
import type { PlayerData, GameModeType } from '@/types';
// Dynamic imports for animation components to prevent SSR issues
const ScorePopup = dynamic(() => import('@/components/game/ScorePopup').then(mod => ({ default: mod.ScorePopup })), { ssr: false });
const FootballFx = dynamic(() => import('@/components/game/FootballFx').then(mod => ({ default: mod.FootballFx })), { ssr: false });
import { FootballLoader } from '@/components/game/FootballLoader';
import { cn } from '@/lib/utils'; // For conditional class names
import { TimeChangePopup } from '@/components/game/TimeChangePopup';
import { TransitionCountdown } from '@/components/game/TransitionCountdown';
import { flushSync } from 'react-dom';

// Dynamic imports for animation components to prevent SSR issues  
const GlobalMultiplayerScoreAnimation = dynamic(() => import('@/components/game/GlobalMultiplayerScoreAnimation').then(mod => ({ default: mod.GlobalMultiplayerScoreAnimation })), { ssr: false });
const GlobalFallbackAnimation = dynamic(() => import('@/components/game/GlobalFallbackAnimation').then(mod => ({ default: mod.GlobalFallbackAnimation })), { ssr: false });
import { useElementPosition } from '@/hooks/useElementPosition';
import { useOnlineStatus } from '@/hooks/useOnlineStatus';
import { DisconnectedOverlay } from '@/components/ui/DisconnectedOverlay';
import AuthModal, { AuthModalRef } from '@/components/auth/AuthModal';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabaseClient';
import { useAuth } from '@/providers/AuthProvider';

import { RealtimeChannel } from '@supabase/supabase-js';
import { useSearchParams, useRouter } from 'next/navigation';


type OverallGameType = 'single-player' | 'multiplayer';
type MultiplayerPanelState = 'lobby_list' | 'in_room';
type CenterPanelMpState = 'lobby_list_detail' | 'expanded_leaderboard' | 'mp_game_active';
type MultiplayerGameMode = 'competitive' | 'cooperative';

// Define Leaderboard types
interface LeaderboardEntry {
  rank: number;
  username: string;
  score: number;
  userId?: string;
}

interface RegionalLeaderboard {
  regionName: string;
  entries: LeaderboardEntry[];
}

// Update the game player types to use a single consistent type
type GamePlayer = {
  user_id: string;
  is_connected: boolean;
  is_ready?: boolean;
  profile?: { username: string | null } | null;
};

// Add type for answer
type GameAnswer = {
  userId: string;
  questionId: string;
  choiceName: string;
  timestamp: number;
  isCorrect: boolean;
};

// Update GameRoom interface to use GameAnswer type
interface GameRoom {
  id: string;
  created_at: string;
  status: 'waiting' | 'active' | 'finished';
  host_id: string;
  multiplayer_mode: MultiplayerGameMode | null;
  title: string | null;
  room_code: string | null;
  max_players: number;
  profiles: { username: string | null } | null;
  game_players: GamePlayer[];
  player_count: number;
  connected_players: number;
  original_player_ids?: string[] | null; // Array of user_ids who were in the room when game started
  current_question_data?: PlayerQuestion;
  player_scores?: Record<string, number>;
  player_bonus_levels?: Record<string, number>;
  current_round_answers?: GameAnswer[];
  current_round_number?: number;
  game_start_timestamp?: string | null;
  current_round_ends_at?: string | null;
  transition_until?: string | null; // When transition period ends
  next_question_data?: PlayerQuestion | null; // Pre-generated next question
  question_sequence?: number; // Sequence number to prevent stale updates
  question_started_at?: string | null; // When the current question started
  last_activity_timestamp?: string | null; // Last activity timestamp
}

// Add PlayerChoice type
interface PlayerChoice {
  name: string;
  isCorrect: boolean;
}

// Add PlayerQuestion type
interface PlayerQuestion {
  questionId: string; // Unique ID for this question instance
  correctPlayerId: number;
  imageUrl: string;
  choices: PlayerChoice[];
  correctChoiceName: string; // Added for answer validation
  correctPlayer?: PlayerData; // Optional, for client-side display if needed
}

// Add type definition at the top level
type UserProfile = {
  id: string;
  username: string | null;
};

// Add new type for players in room - consolidated and enhanced
type PlayerInRoom = {
  user_id: string;
  profile: { username: string | null } | null;
  is_ready?: boolean;
  is_connected: boolean;
  gamePlayerId?: string; // The ID from the game_players table (optional for backward compatibility)
  roomId?: string; // Room ID (optional for backward compatibility)
  joined_at?: string; // Join timestamp (optional for backward compatibility)
};

// Add new types for game settings
type GameDuration = 30 | 60 | 90 | 120;
type MaxPlayers = 2 | 4 | 6 | 8;

// Add state for tracking animated answers
interface AnimatedAnswersState {
  [questionId: string]: Set<string>; // questionId -> Set of userIds that have been animated
}

// Add interface for enhanced animation data
interface PlayerAnimationData {
  trigger: number;
  scoreIncrease: number;
  bonusLevel: number;
  questionId: string;
}

interface EnhancedAnimatedAnswersState {
  [userId: string]: PlayerAnimationData;
}

// Define the possible connection states for the realtime subscriptions
type ConnectionStatus = 'INITIALIZING' | 'CONNECTED' | 'RECONNECTING' | 'OFFLINE';

// Connection Status Indicator Component
const ConnectionStatusIndicator = ({ status }: { status: ConnectionStatus }) => {
  switch (status) {
    // In the ideal state, render nothing at all.
    case 'CONNECTED':
    case 'INITIALIZING':
      return null;

    // When reconnecting, show a calm, temporary message.
    case 'RECONNECTING':
      return (
        <div className="reconnecting-indicator flex items-center justify-center gap-2 bg-yellow-900/50 text-yellow-200 p-2 rounded text-xs mb-3 text-center w-full border border-yellow-600/30">
          <div className="spinner w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
          <span>Reconnecting...</span>
        </div>
      );

    // If you implement a fully offline state, show a persistent message.
    case 'OFFLINE':
      return (
        <div className="offline-indicator bg-red-900/50 text-red-200 p-2 rounded text-xs mb-3 text-center w-full border border-red-600/30">
          <span>You are offline. Please check your connection.</span>
        </div>
      );

    default:
      return null;
  }
};



function HomePageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Overall game type state (moved to top)
  const [selectedOverallGameType, setSelectedOverallGameType] = useState<OverallGameType>('single-player');

  // Add debug logging for state changes
  console.log("HomePageContent render - Current selectedOverallGameType:", selectedOverallGameType);

  // Add hasMounted state for hydration handling
  const [hasMounted, setHasMounted] = useState(false);

  // Add clientSearchParams to prevent hydration mismatch
  const [clientSearchParams, setClientSearchParams] = useState<URLSearchParams | null>(null);

  // Add ref for AuthModal
  const authModalRef = useRef<AuthModalRef>(null);

  // Set up clientSearchParams to prevent hydration mismatch
  useEffect(() => {
    // Only access search params on client side
    if (typeof window !== 'undefined') {
      setClientSearchParams(new URLSearchParams(window.location.search));
    }
  }, []);

  // Layer 1: The Proactive Client - Track browser online status
  const isOnline = useOnlineStatus();


  // Get state and actions from the store
  const {
    loadPlayers,
    setGameMode,
    nextQuestion,
    submitAnswer,
    resetCurrentModeGame,
    currentQuestion,
    score,
    streak,
    bestStreak,
    bestNormalScore,
    bestTimedScore,
    isAnswered,
    recentAnswers,
    gameStatus,
    activeGameMode,
    lastScoreChange,
    animationTrigger,
    isLoadingInitialGame,
    loadingMessage,
    timer,
    isCountdownActive,
    countdownValue,
    userChoiceName,
    lastTimeChange,
    timeChangeAnimationTrigger,
  } = useGameStore();

  // Existing state
  const [selectedPlayerInfo, setSelectedPlayerInfo] = useState<PlayerData | null>(null);
  const [viewingRecentPlayer, setViewingRecentPlayer] = useState<PlayerData | null>(null);

  // New multiplayer state
  const [multiplayerPanelState, setMultiplayerPanelState] = useState<MultiplayerPanelState>('lobby_list');
  const [centerPanelMpState, setCenterPanelMpState] = useState<CenterPanelMpState>('lobby_list_detail');
  const [activeRoomId, setActiveRoomId] = useState<string | null>(null);
  const [selectedRoomForDetail, setSelectedRoomForDetail] = useState<GameRoom | null>(null);
  // Get authentication state from AuthProvider
  const { user, isLoading: isAuthLoading, profile: userProfile, isLoadingProfile } = useAuth();
  const [gameRooms, setGameRooms] = useState<GameRoom[]>([]);
  const [isLoadingRooms, setIsLoadingRooms] = useState(false);
  const [errorMp, setErrorMp] = useState<string | null>(null);
  const [lobbyFetchError, setLobbyFetchError] = useState<boolean>(false);
  const [newRoomMode, setNewRoomMode] = useState<MultiplayerGameMode>('competitive');

  // Leaderboard States
  const [personalRecords, setPersonalRecords] = useState<LeaderboardEntry[]>([]);
  const [globalLeaderboard, setGlobalLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [regionalLeaderboards, setRegionalLeaderboards] = useState<RegionalLeaderboard[]>([]);
  const [userRegion, setUserRegion] = useState<string | null>(null);
  const [expandedLeaderboardData, setExpandedLeaderboardData] = useState<{title: string, entries: LeaderboardEntry[]} | null>(null);

  // Profile data now comes from AuthProvider - removed local state

  // Add new state for players in room
  const [playersInRoom, setPlayersInRoom] = useState<PlayerInRoom[]>([]);
  const [isLoadingPlayers, setIsLoadingPlayers] = useState(false);

  // Add new state for current room's game data
  const [currentRoomGameData, setCurrentRoomGameData] = useState<GameRoom | null>(null);

  // Add new state for game settings
  const [selectedGameDuration, setSelectedGameDuration] = useState<GameDuration>(60);
  const [selectedMaxPlayers, setSelectedMaxPlayers] = useState<MaxPlayers>(4);
  const [isCreatingRoom, setIsCreatingRoom] = useState(false);
  const [isStartingGame, setIsStartingGame] = useState(false); // New state for preventing double-start



  // Add state for tracking animated answers
  const [animatedAnswers, setAnimatedAnswers] = useState<AnimatedAnswersState>({});

  // Add state for enhanced animation data
  const [enhancedAnimatedAnswers, setEnhancedAnimatedAnswers] = useState<EnhancedAnimatedAnswersState>({});

  // Add state for answer submission
  const [isSubmittingAnswer, setIsSubmittingAnswer] = useState(false);
  const [hasSubmittedCurrentRound, setHasSubmittedCurrentRound] = useState(false);
  const submissionInProgressRef = useRef(false);
  const previousQuestionIdRef = useRef<string | null>(null);
  const transitionInProgressRef = useRef(false);
  const transitionedQuestionIdRef = useRef<string | null>(null);
  const transitionTimerRef = useRef<NodeJS.Timeout | null>(null);
  const hardCapTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [submittedAnswerForQuestion, setSubmittedAnswerForQuestion] = useState<string | null>(null);

  // Add state for next question functionality
  const [isAdvancingToNextQuestion, setIsAdvancingToNextQuestion] = useState(false);

  // Add optimistic answer state for snappy UI updates
  const [optimisticAnswer, setOptimisticAnswer] = useState<{
    userId: string;
    questionId: string;
    choiceName: string;
    timestamp: number;
    isCorrect: boolean;
    isPending: boolean;
  } | null>(null);

  // Add state for preventing multiple ready state submissions
  const [isSubmittingReady, setIsSubmittingReady] = useState(false);

  // Add state for debug show answer feature
  const [showDebugAnswer, setShowDebugAnswer] = useState(false);
  
  // Add state for preventing multiple leave room submissions
  const [isLeavingRoom, setIsLeavingRoom] = useState(false);

  // Add state for join/rejoin loading feedback
  const [isJoiningOrRejoiningRoom, setIsJoiningOrRejoiningRoom] = useState(false);

  // Add state for preventing URL parameter interference during room creation
  const [isCreatingAndJoiningRoom, setIsCreatingAndJoiningRoom] = useState(false);

  // Add connection status state for realtime subscriptions
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('INITIALIZING');

  // 1. Add setHasAnswered state
  const [hasAnswered, setHasAnswered] = useState(false);

  // 2. Add triggeredAnimationsRef
  const triggeredAnimationsRef = useRef<Set<string>>(new Set());

  // Add useRef for tracking subscription channels to prevent "nuke and pave" issues
  const roomSubscriptionsRef = useRef<RealtimeChannel[]>([]);

  // Add ref to track loading state synchronously to prevent multiple concurrent fetches
  const isLoadingRoomsRef = useRef(false);
  
  // Add ref to track if we're currently starting a game to pause periodic fetches
  const isStartingGameRef = useRef(false);
  
  // Add ref for syncFullRoomState to avoid initialization issues
  const syncFullRoomStateRef = useRef<((roomId: string, caller: string) => Promise<void>) | null>(null);
  
  // Create refs for functions to prevent re-subscriptions
  const fetchAndSetGameRoomsRef = useRef<(() => Promise<void>) | null>(null);
  const fetchPlayersInActiveRoomRef = useRef<((roomId: string, caller?: string) => Promise<void>) | null>(null);

  // Removed authEffectRan ref since we now use AuthProvider for auth management

  // 3. Add setGlobalAnimations and globalAnimations
  const [globalAnimations, setGlobalAnimations] = useState<Array<{
    id: string;
    type: 'enhanced' | 'fallback';
    trigger: number;
    scoreIncrease: number;
    bonusLevel: number;
    originPosition: { x: number; y: number };
  }>>([]);

  // 4. Change getElementPosition to useElementPosition
  const getElementPosition = useElementPosition();

  // 5. Add animatingAnswers and setAnimatingAnswers
  const [animatingAnswers, setAnimatingAnswers] = useState<Set<string>>(new Set());
  const [landedAnswers, setLandedAnswers] = useState<Set<string>>(new Set());
  
  // Track first correct answerer per question for football animation
  const [firstCorrectAnswererByQuestion, setFirstCorrectAnswererByQuestion] = useState<Map<string, string>>(new Map());


  // 6. Add allAnswers using useMemo
  const allAnswers = useMemo(() => {
    const realTimeAnswers: (GameAnswer & { isOptimistic?: boolean })[] = Array.isArray(currentRoomGameData?.current_round_answers)
      ? currentRoomGameData.current_round_answers.map(answer => ({ ...answer, isOptimistic: false }))
      : [];

    // Include optimistic answer if it exists and matches current question
    const currentQuestionId = currentRoomGameData?.current_question_data?.questionId;

    // Use a map to deduplicate answers by user/question combination
    const answerMap = new Map<string, GameAnswer & { isOptimistic?: boolean }>();

    for (const ans of realTimeAnswers) {
      const key = `${ans.userId}-${ans.questionId}`;
      answerMap.set(key, ans);
    }

    if (optimisticAnswer && optimisticAnswer.questionId === currentQuestionId) {
      const key = `${optimisticAnswer.userId}-${optimisticAnswer.questionId}`;
      answerMap.set(key, { ...optimisticAnswer, isOptimistic: true });
    }

    const finalAnswers = Array.from(answerMap.values());
    
    console.log('[ALL_ANSWERS_DEBUG] Constructed allAnswers:', {
      realTimeAnswersCount: realTimeAnswers.length,
      hasOptimisticAnswer: !!optimisticAnswer,
      optimisticMatchesCurrentQuestion: optimisticAnswer?.questionId === currentQuestionId,
      finalAnswersCount: finalAnswers.length,
      currentUserId: user?.id,
      finalAnswers: finalAnswers.map(ans => ({
        userId: ans.userId.substring(0, 8) + '...',
        isCurrentUser: ans.userId === user?.id,
        choiceName: ans.choiceName,
        isCorrect: ans.isCorrect,
        isOptimistic: ans.isOptimistic
      }))
    });

    return finalAnswers;
  }, [currentRoomGameData?.current_round_answers, currentRoomGameData?.current_question_data?.questionId, optimisticAnswer, user?.id]);

  // Track new answers for animation
  useEffect(() => {
    if (!hasMounted) return;

    // Create a set of current answer keys
    const currentAnswerKeys = new Set(
      allAnswers.map(answer => `${answer.userId}-${answer.questionId}`)
    );

    // Find new answers that aren't already animating or landed
    setAnimatingAnswers(prev => {
      const newSet = new Set(prev);
      let hasNewAnswers = false;
      
      // Add new answers - CRITICAL FIX: Check landedAnswers to prevent re-animation
      currentAnswerKeys.forEach(key => {
        // Only animate if not currently animating AND not already landed
        if (!prev.has(key) && !landedAnswers.has(key)) {
          newSet.add(key);
          hasNewAnswers = true;
          console.log('[ANIMATION_FIX] Adding new answer to animate:', key);
        }
      });
      
      // Remove old answers that are no longer current
      Array.from(prev).forEach(key => {
        if (!currentAnswerKeys.has(key)) {
          newSet.delete(key);
        }
      });
      
      return hasNewAnswers || newSet.size !== prev.size ? newSet : prev;
    });

    // Clean up landed answers that are no longer in the current answer list
    setLandedAnswers(prev => {
      const newSet = new Set(prev);
      let hasChanges = false;
      
      // Remove any landed answers that aren't in the current answer list
      Array.from(prev).forEach(key => {
        if (!currentAnswerKeys.has(key)) {
          newSet.delete(key);
          hasChanges = true;
        }
      });
      
      return hasChanges ? newSet : prev;
    });
  }, [allAnswers, hasMounted, landedAnswers]);

  // 7. Add handleNextQuestion if not present
  // NOTE: This function is no longer used for normal multiplayer gameplay
  // The submit-answer-handler now automatically advances the game when the last player submits
  // This prevents browser tab throttling issues with client-side timers
  const handleNextQuestion = useCallback(async () => {
    if (!user?.id || !activeRoomId || isAdvancingToNextQuestion) {
      return;
    }

    // DIAGNOSTIC: Log what we're about to send to the server
    console.log('[DIAGNOSTIC] handleNextQuestion called - BEFORE server call');
    console.log('[DIAGNOSTIC] NOTE: Server-side auto-progression should handle this automatically');
    console.log('[DIAGNOSTIC] Client-side data at time of next question request:', {
      activeRoomId,
      userId: user.id,
      currentRoomGameData: {
        status: currentRoomGameData?.status,
        host_id: currentRoomGameData?.host_id,
        current_question_data: currentRoomGameData?.current_question_data ? {
          questionId: currentRoomGameData.current_question_data.questionId,
          correctPlayerId: currentRoomGameData.current_question_data.correctPlayerId,
          choicesCount: currentRoomGameData.current_question_data.choices.length
        } : null,
        current_round_answers: currentRoomGameData?.current_round_answers,
        current_round_answers_type: Array.isArray(currentRoomGameData?.current_round_answers) ? 'array' : typeof currentRoomGameData?.current_round_answers,
        current_round_answers_length: Array.isArray(currentRoomGameData?.current_round_answers) ? currentRoomGameData.current_round_answers.length : 'N/A'
      },
      playersInRoom: {
        count: playersInRoom.length,
        userIds: playersInRoom.map(p => p.user_id)
      },
      // Calculate the same logic as the UI
      clientSideCalculation: (() => {
        const realTimeAnswers = Array.isArray(currentRoomGameData?.current_round_answers)
          ? currentRoomGameData.current_round_answers
          : [];
        const currentQuestionId = currentRoomGameData?.current_question_data?.questionId;
        const answersForCurrentQuestion = realTimeAnswers.filter(answer =>
          answer.questionId === currentQuestionId
        );
        return {
          realTimeAnswersType: Array.isArray(realTimeAnswers) ? 'array' : typeof realTimeAnswers,
          realTimeAnswersLength: realTimeAnswers.length,
          currentQuestionId,
          answersForCurrentQuestion: answersForCurrentQuestion,
          answersForCurrentQuestionCount: answersForCurrentQuestion.length,
          playersInRoomCount: playersInRoom.length,
          allPlayersSubmitted: answersForCurrentQuestion.length === playersInRoom.length
        };
      })()
    });

    setIsAdvancingToNextQuestion(true);

    try {
      const { data, error } = await supabase.functions.invoke('next-question-handler', {
        body: { roomId: activeRoomId },
      });

      console.log('[DIAGNOSTIC] Server response received:', { data, error });

      if (error) {
        console.log('[DIAGNOSTIC] Server returned error:', error);
        setErrorMp(`Failed to advance to next question: ${error.message}`);
      } else {
        console.log('[DIAGNOSTIC] Server call successful:', data);
      }
    } catch (e) {
      console.log('[DIAGNOSTIC] Exception during server call:', e);
      setErrorMp('Failed to advance to next question. Please try again.');
    } finally {
      setIsAdvancingToNextQuestion(false);
    }
  }, [user?.id, activeRoomId, isAdvancingToNextQuestion, currentRoomGameData, playersInRoom]);

  // **CRITICAL: TOP-LEVEL RENDER STATE CHECK - This should show the actual state being used in this render cycle**
  console.log('[RENDER HOST] *** TOP-LEVEL RENDER STATE CHECK *** HomePageContent render cycle. playersInRoom:', JSON.parse(JSON.stringify(playersInRoom)));

  // **CRITICAL: COMPREHENSIVE RENDER LOGGING - Track every re-render and state changes**
  const currentRoomDetails = gameRooms.find(room => room.id === activeRoomId);
  const isCurrentUserHost = currentRoomDetails?.host_id === user?.id;
  const renderLogPrefix = isCurrentUserHost ? '[RENDER HOST]' : '[RENDER]';
  
  console.log(`${renderLogPrefix} *** HomePageContent RE-RENDER *** Component render cycle:`, {
    timestamp: new Date().toISOString(),
    renderNumber: Math.floor(Date.now() / 1000), // Approximate render sequence
    keyStateValues: {
      selectedOverallGameType,
      multiplayerPanelState,
      centerPanelMpState,
      activeRoomId,
      hasUser: !!user,
      userId: user?.id,
      isCurrentUserHost,
      playersInRoomCount: playersInRoom.length,
      playersInRoomUserIds: playersInRoom.map(p => p.user_id),
      playersInRoomUsernames: playersInRoom.map(p => p.profile?.username || 'NO_USERNAME'),
      playersReady: playersInRoom.filter(p => p.is_ready).length,
      currentRoomStatus: currentRoomGameData?.status,
      isLoadingPlayers,
      hasCurrentRoomGameData: !!currentRoomGameData
    },
    componentWillRender: {
      shouldShowPlayerList: selectedOverallGameType === 'multiplayer' && multiplayerPanelState === 'in_room' && currentRoomGameData?.status === 'waiting',
      shouldShowStartButton: selectedOverallGameType === 'multiplayer' && isCurrentUserHost && currentRoomGameData?.status === 'waiting',
      shouldShowNeedMorePlayersMessage: selectedOverallGameType === 'multiplayer' && isCurrentUserHost && playersInRoom.length < 2,
      playersInRoomIsEmpty: playersInRoom.length === 0,
      allPlayersReady: playersInRoom.every(p => p.is_ready)
    }
  });

  // **CRITICAL: Add specific logging for the "Need X more players" logic**
  if (selectedOverallGameType === 'multiplayer' && isCurrentUserHost && currentRoomGameData?.status === 'waiting') {
    const playerCountForButton = playersInRoom.length;
    const needMorePlayers = playerCountForButton < 2;
    const playersNeeded = needMorePlayers ? 2 - playerCountForButton : 0;
    
    console.log(`${renderLogPrefix} [START_BUTTON_LOGIC] Host rendering Start Game button logic:`, {
      playerCountForButton,
      needMorePlayers,
      playersNeeded,
      buttonTextWillBe: needMorePlayers ? `Need ${playersNeeded} more player(s)` : (playersInRoom.every(p => p.is_ready) ? "Start Game" : "Waiting for all to ready..."),
      buttonWillBeDisabled: needMorePlayers || !playersInRoom.every(p => p.is_ready) || isStartingGame,
      playersInRoomDetailed: playersInRoom.map(p => ({
        userId: p.user_id,
        username: p.profile?.username,
        isReady: p.is_ready,
        isConnected: p.is_connected
      })),
      timestamp: new Date().toISOString()
    });
  }

  // **CRITICAL: Add specific logging for the player list rendering**
  if (selectedOverallGameType === 'multiplayer' && multiplayerPanelState === 'in_room' && currentRoomGameData?.status === 'waiting') {
    console.log(`${renderLogPrefix} [PLAYER_LIST_LOGIC] Rendering player list in waiting room:`, {
      playersInRoomCount: playersInRoom.length,
      maxPlayers: currentRoomGameData.max_players,
      displayString: `Players (${playersInRoom.length}/${currentRoomGameData.max_players || 8})`,
      isLoadingPlayers,
      willShowLoadingMessage: isLoadingPlayers,
      willShowWaitingMessage: !isLoadingPlayers && playersInRoom.length === 0,
      willShowPlayerList: !isLoadingPlayers && playersInRoom.length > 0,
      playersToRender: playersInRoom.map(p => ({
        userId: p.user_id,
        username: p.profile?.username || `Player...${p.user_id.slice(-4)}`,
        isReady: p.is_ready,
        isCurrentUser: p.user_id === user?.id,
        isHost: currentRoomGameData?.host_id === p.user_id
      })),
      timestamp: new Date().toISOString()
    });
  }

  // Load player data on initial mount
  useEffect(() => {
    // Initial load only if players aren't loaded yet
    if (!useGameStore.getState().players.length) {
      loadPlayers();
    }
  }, [loadPlayers]);

  // Add effect for client-side mounting
  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Add enhanced sign-out handler before the existing useEffect
  const handleSignOut = async () => {
    console.log('[SignOut] Enhanced sign-out process initiated', {
      hasActiveRoom: !!activeRoomId,
      activeRoomId,
      userId: user?.id,
      timestamp: new Date().toISOString()
    });

    // Note: Room cleanup will happen automatically via the auth listener's comprehensive state reset
    // The auth listener will clear all multiplayer states including activeRoomId when user signs out
    console.log('[SignOut] Room cleanup will be handled by auth listener state reset');

    // Perform Supabase sign-out
    console.log('[SignOut] Proceeding with Supabase sign-out');
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('[SignOut] Error during Supabase sign-out:', error);
      setErrorMp(`Sign-out error: ${error.message}`);
    } else {
      console.log('[SignOut] Supabase sign-out successful - auth listener will handle state reset');
    }
  };

  // Add useEffect to handle sign-out state cleanup
  useEffect(() => {
    if (!user) {
      // Profile clearing is now handled by AuthProvider
      
      // Switch to single-player if currently in multiplayer
      if (selectedOverallGameType === 'multiplayer') {
        setSelectedOverallGameType('single-player');
      }
      
      // Reset all multiplayer state
      setActiveRoomId(null);
      setCurrentRoomGameData(null);
      setPlayersInRoom([]);
      setSelectedRoomForDetail(null);
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setGameRooms([]);
      setIsLoadingRooms(false);
      setErrorMp(null);
      setIsLeavingRoom(false);
      setIsSubmittingReady(false);
      setIsCreatingRoom(false);
      setIsStartingGame(false);
      setIsJoiningOrRejoiningRoom(false);
      setIsCreatingAndJoiningRoom(false);
      setExpandedLeaderboardData(null);
      setPersonalRecords([]);
      setGlobalLeaderboard([]);
      setRegionalLeaderboards([]);
      setUserRegion(null);
      setAnimatedAnswers({});
      setEnhancedAnimatedAnswers({});
    }
  }, [user, selectedOverallGameType]);

  // NOTE: Profile fetching is now handled by AuthProvider - no longer needed here

  // Add automatic disconnect detection and connection management
  useEffect(() => {
    if (!user?.id || !activeRoomId) {
      return; // No cleanup needed if not logged in or not in a room
    }

    console.log('[DISCONNECT_DETECTION] Setting up disconnect detection for user:', {
      userId: user.id,
      activeRoomId,
      timestamp: new Date().toISOString()
    });

    // Function to mark player as disconnected
    const markPlayerDisconnected = async (reason: string) => {
      console.log(`[DISCONNECT_DETECTION] Marking player as disconnected. Reason: ${reason}`, {
        userId: user.id,
        activeRoomId,
        timestamp: new Date().toISOString()
      });

      try {
        const { error } = await supabase
          .from('game_players')
          .update({
            is_connected: false,
            last_seen_at: new Date().toISOString()
          })
          .eq('room_id', activeRoomId)
          .eq('user_id', user.id);

        if (error) {
          console.error('[DISCONNECT_DETECTION] Error marking player as disconnected:', error);
        } else {
          console.log('[DISCONNECT_DETECTION] Successfully marked player as disconnected');
        }
      } catch (e) {
        console.error('[DISCONNECT_DETECTION] Exception marking player as disconnected:', e);
      }
    };

    // Handle browser tab close/refresh
    const handleBeforeUnload = () => {
      console.log('[DISCONNECT_DETECTION] beforeunload event triggered');

      // Mark player as disconnected (this is a best-effort attempt)
      // Note: This may not always complete due to browser limitations
      markPlayerDisconnected('beforeunload');

      // Don't show confirmation dialog for multiplayer games
      // Users should be able to leave/refresh freely and reconnect
    };

    // Handle page visibility changes (tab switching, minimizing)
    const handleVisibilityChange = async () => {
      try {
        if (document.hidden) {
          console.log('[DISCONNECT_DETECTION] Page became hidden (tab switch/minimize)');
          // Let Supabase handle the disconnect naturally
        } else {
          console.log('[DISCONNECT_DETECTION] Page became visible.');

          // The Supabase client will automatically refresh the token and reconnect WebSockets
          // We just need to ensure our application state is in sync
          const { data: { user } } = await supabase.auth.getUser();

          if (user && activeRoomId) {
            console.log('[DISCONNECT_DETECTION] User is in an active room. Checking game state before sync.');
            
            // Check if we're in a critical game state where all players have submitted
            // and we're waiting for the host to advance to the next question
            if (currentRoomGameData?.status === 'active' && currentRoomGameData?.current_question_data) {
              const realTimeAnswers: GameAnswer[] = Array.isArray(currentRoomGameData.current_round_answers)
                ? currentRoomGameData.current_round_answers
                : [];
              
              const currentQuestionId = currentRoomGameData.current_question_data.questionId;
              const answersForCurrentQuestion = realTimeAnswers.filter(answer =>
                answer.questionId === currentQuestionId
              );
              
              const allPlayersSubmitted = answersForCurrentQuestion.length === playersInRoom.length && playersInRoom.length > 0;
              
              if (allPlayersSubmitted) {
                console.log('[DISCONNECT_DETECTION] All players have submitted. Skipping full state sync to preserve game flow.');
                // Only update connection status, don't sync full state
                await supabase
                  .from('game_players')
                  .update({ is_connected: true, last_seen_at: new Date().toISOString() })
                  .match({ room_id: activeRoomId, user_id: user.id });
                return;
              }
            }
            
            // Update presence in the database
            await supabase
              .from('game_players')
              .update({ is_connected: true, last_seen_at: new Date().toISOString() })
              .match({ room_id: activeRoomId, user_id: user.id });

            // SIMPLIFIED: Just verify connection is alive, don't sync state
            // The server is the source of truth for game progression
            console.log('[DISCONNECT_DETECTION] Tab visible again - verifying connection');
            // Connection will auto-reconnect if needed via Supabase realtime
          }
        }
      } catch (err) {
        console.warn(
          '[DISCONNECT_DETECTION] handleVisibilityChange encountered an error:',
          err instanceof Error ? err.message : err
        );
      }
    };

    // Add event listeners with promise rejection handling
    window.addEventListener('beforeunload', handleBeforeUnload);
    const visibilityWrapper = () => {
      handleVisibilityChange().catch(err =>
        console.warn(
          '[DISCONNECT_DETECTION] handleVisibilityChange rejected:',
          err instanceof Error ? err.message : err
        )
      );
    };
    document.addEventListener('visibilitychange', visibilityWrapper);

    // Cleanup function
    return () => {
      console.log('[DISCONNECT_DETECTION] Cleaning up disconnect detection listeners');
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', visibilityWrapper);
    };
  }, [user?.id, activeRoomId]); // Re-run when user or room changes

  // Add fetchAndSetGameRooms function after the state declarations
  const fetchAndSetGameRooms = useCallback(async () => {
    // Guard against auth loading state
    if (isAuthLoading) {
      console.log('[LOBBY_FETCH] Skipping fetchAndSetGameRooms - auth still loading');
      return;
    }

    // Guard against missing user
    if (!user?.id) {
      console.log('[LOBBY_FETCH] Skipping fetchAndSetGameRooms - no authenticated user');
      isLoadingRoomsRef.current = false; // Reset ref
      setIsLoadingRooms(false); // Reset loading state if no user
      return;
    }

    // **CRITICAL FIX: Use ref for synchronous loading state tracking**
    // This prevents multiple concurrent calls when React setState is async
    console.log('[LOBBY_FETCH] *** LOADING STATE CHECK *** Current isLoadingRoomsRef:', isLoadingRoomsRef.current);
    
    if (isLoadingRoomsRef.current) {
      console.log('[LOBBY_FETCH] *** EARLY RETURN *** Fetch already in progress (ref), skipping to prevent redundant calls');
      return;
    }

    // Set both ref and state to indicate loading has started
    isLoadingRoomsRef.current = true;
    setIsLoadingRooms(true);
    console.log('[LOBBY_FETCH] Starting new fetch, setting loading to true (both ref and state)');

    console.log('[LOBBY_FETCH] *** CRITICAL DEBUG START *** fetchAndSetGameRooms CALLED for user:', {
      userId: user?.id,
      timestamp: new Date().toISOString(),
      userIsAuthenticated: !!user,
      purpose: 'Investigating lobby player count display issue'
    });

    console.log('[LOBBY_FETCH] *** QUERY CONSTRUCTION ANALYSIS *** About to construct Supabase query');
    // setIsLoadingRooms(true); // REMOVED: Already set above using functional setState
    setErrorMp(null);
    setLobbyFetchError(false);
    
    // **CRITICAL FIX**: Fetch individual player records instead of count aggregation
    // This should work better with RLS policies
    console.log('[LOBBY_FETCH] *** CRITICAL QUERY DETAILS *** Constructing query with the following specifications:');
    console.log('[LOBBY_FETCH] - Table: game_rooms');
    console.log('[LOBBY_FETCH] - Status filter: [waiting, active] (to show joinable rooms)');
    console.log('[LOBBY_FETCH] - Player data method: Fetching individual game_players records with user_id and is_connected');
    console.log('[LOBBY_FETCH] - User context: Fetching for user', user?.id);
    console.log('[LOBBY_FETCH] - Expected behavior: Should return ALL waiting/active rooms with player data for reconnection logic');
    
    // **ENHANCED SELECT STRING**: Fetch individual player records for reconnection logic
    const selectQueryString = `
      id,
      title,
      status,
      room_code,
      multiplayer_mode,
      host_id,
      created_at,
      max_players,
      original_player_ids,
      profiles:host_id (
        username
      ),
      game_players (
        user_id,
        is_connected
      )
    `;
    
    console.log('[LOBBY_FETCH] *** SELECT STRING VERIFICATION *** Using exact select string:', {
      selectQueryString: selectQueryString.trim(),
      includesGamePlayersRecords: selectQueryString.includes('game_players ('),
      includesUserIdField: selectQueryString.includes('user_id'),
      includesIsConnectedField: selectQueryString.includes('is_connected'),
      userContext: user?.id
    });

    // Base query: select rooms that are 'waiting' or 'active' for rejoining
    const query = supabase
      .from('game_rooms')
      .select(selectQueryString)
      .in('status', ['waiting', 'active']) // Show waiting AND active games for rejoining
      .order('created_at', { ascending: false });

    // **CRITICAL**: Log the exact query structure before execution
    console.log('[LOBBY_FETCH] *** QUERY OBJECT INSPECTION *** Final query object constructed:', {
      queryDetails: {
        table: 'game_rooms',
        selectFields: selectQueryString.split('\n').map(s => s.trim()).filter(s => s),
        statusFilter: ['waiting', 'active'],
        orderBy: 'created_at desc',
        playerDataMethod: 'game_players individual records',
        userContext: user?.id,
        expectedToSeeRoomsHostedByOthers: true,
        potentialIssue: 'If this returns empty game_players arrays, check game_players RLS policies for individual record access'
      }
    });

    console.log('[LOBBY_FETCH] *** ABOUT TO EXECUTE QUERY *** Calling Supabase with constructed query');
    
    try {
      const { data: fetchedRooms, error } = await query;

      console.log('[LOBBY_FETCH] *** RAW QUERY RESULT *** Supabase query completed:', {
        success: !error,
        error: error ? JSON.stringify(error, null, 2) : null,
        resultCount: fetchedRooms?.length || 0,
        userWhoExecutedQuery: user?.id,
        timestamp: new Date().toISOString()
      });

      // **CRITICAL DEBUG**: Log the raw data structure from Supabase
      if (fetchedRooms && fetchedRooms.length > 0) {
        console.log('[LOBBY_FETCH] *** RAW DATA STRUCTURE INSPECTION *** First room raw data:', {
          firstRoom: JSON.stringify(fetchedRooms[0], null, 2),
          gamePlayersStructure: fetchedRooms[0]?.game_players,
          gamePlayersType: typeof fetchedRooms[0]?.game_players,
          gamePlayersIsArray: Array.isArray(fetchedRooms[0]?.game_players),
          hasPlayerRecords: Array.isArray(fetchedRooms[0]?.game_players) && fetchedRooms[0]?.game_players.length > 0,
          userContext: user?.id
        });
      }

      if (error) {
        console.error('[LOBBY_FETCH] *** QUERY ERROR *** Error fetching game_rooms:', {
          errorMessage: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          errorHint: error.hint,
          userContext: user?.id,
          possibleCauses: [
            'RLS policy blocking access to game_rooms',
            'RLS policy blocking game_players count aggregation',
            'Network connectivity issue',
            'Database permissions problem',
            'Query syntax error'
          ]
        });
        setGameRooms([]); // Set to empty on error
        setLobbyFetchError(true);
        setErrorMp(`Failed to fetch room details: ${error.message}`);
      } else {
        console.log('[LOBBY_FETCH] *** SUCCESSFUL RAW DATA *** Query successful, processing results:', {
          totalRoomsFromDB: fetchedRooms?.length || 0,
          userExecutingQuery: user?.id,
          roomsBreakdown: fetchedRooms?.map(room => ({
            roomId: room.id.substring(0, 8) + '...',
            title: room.title,
            status: room.status,
            hostId: room.host_id.substring(0, 8) + '...',
            rawGamePlayersData: room.game_players,
            isHostedByCurrentUser: room.host_id === user?.id,
            isHostedByOtherUser: room.host_id !== user?.id
          })) || [],
          criticalCheck: {
            shouldSeeFreshsRoom: 'Check if any room has hostId matching fresh user',
            fresh2CanSeeRoomsHostedByOthers: fetchedRooms?.some(room => room.host_id !== user?.id) || false
          }
        });
        
        const enrichedRooms = fetchedRooms?.map(room => {
          // **CRITICAL PLAYER COUNT EXTRACTION**: Handle the count aggregation result
          let playerCount = 0;
          
          console.log('[LOBBY_FETCH] *** PLAYER COUNT EXTRACTION *** Processing room for player count:', {
            roomId: room.id,
            roomTitle: room.title,
            hostId: room.host_id,
            rawGamePlayersData: room.game_players,
            gamePlayersType: typeof room.game_players,
            gamePlayersIsArray: Array.isArray(room.game_players),
            gamePlayersLength: room.game_players?.length,
            firstElementIfArray: Array.isArray(room.game_players) ? room.game_players[0] : null
          });
          
          // Process individual player records instead of count aggregation
          if (Array.isArray(room.game_players)) {
            playerCount = room.game_players.length;
            const connectedPlayerCount = room.game_players.filter(p => p.is_connected).length;
            console.log('[LOBBY_FETCH] *** PLAYER RECORDS SUCCESS *** Extracted player data:', {
              roomId: room.id,
              totalPlayers: playerCount,
              connectedPlayers: connectedPlayerCount,
              playerRecords: room.game_players.map(p => ({ user_id: p.user_id, is_connected: p.is_connected }))
            });
          } else {
            console.warn('[LOBBY_FETCH] *** PLAYER RECORDS ERROR *** game_players is not an array:', {
              roomId: room.id,
              gamePlayersType: typeof room.game_players,
              gamePlayersValue: room.game_players,
              fallbackCount: playerCount
            });
          }
          
          console.log('[LOBBY_FETCH] *** ROOM ENRICHMENT *** Processing room for display:', {
            roomId: room.id,
            roomTitle: room.title,
            hostId: room.host_id,
            status: room.status,
            finalPlayerCount: playerCount,
            maxPlayers: room.max_players,
            isHostedByCurrentUser: room.host_id === user?.id,
            profilesData: room.profiles
          });
          
          // Handle profiles data safely - Supabase joins can return array or single object
          let hostUsername: string | null = null;
          if (room.profiles) {
            if (Array.isArray(room.profiles)) {
              hostUsername = room.profiles[0]?.username || null;
            } else {
              hostUsername = (room.profiles as { username: string | null })?.username || null;
            }
          }
          
          return {
            id: room.id,
            created_at: room.created_at,
            status: room.status,
            host_id: room.host_id,
            multiplayer_mode: room.multiplayer_mode,
            title: room.title,
            room_code: room.room_code,
            max_players: room.max_players,
            original_player_ids: room.original_player_ids, // **CRITICAL**: Include original player IDs for reconnection logic
            profiles: hostUsername ? { username: hostUsername } : null,
            game_players: room.game_players || [], // **CRITICAL**: Keep individual player data for reconnection logic
            player_count: playerCount,
            connected_players: Array.isArray(room.game_players) ? room.game_players.filter(p => p.is_connected).length : 0
          };
        }) || [];
        
        console.log('[LOBBY_FETCH] *** FINAL ENRICHED RESULTS *** Setting gameRooms state:', {
          enrichedRoomsCount: enrichedRooms.length,
          userContext: user?.id,
          finalRoomsForUI: enrichedRooms.map(room => ({
            id: room.id.substring(0, 8) + '...',
            title: room.title,
            hostId: room.host_id.substring(0, 8) + '...',
            status: room.status,
            playerCount: room.player_count,
            connectedPlayers: room.connected_players,
            isOwnedByCurrentUser: room.host_id === user?.id,
            displayText: `Players: ${room.player_count}/${room.max_players || 8}`
          })),
          criticalAnalysis: {
            totalRoomsToShow: enrichedRooms.length,
            roomsHostedByCurrentUser: enrichedRooms.filter(r => r.host_id === user?.id).length,
            roomsHostedByOthers: enrichedRooms.filter(r => r.host_id !== user?.id).length,
            waitingRooms: enrichedRooms.filter(r => r.status === 'waiting').length,
            roomsWithZeroPlayers: enrichedRooms.filter(r => r.player_count === 0).length,
            roomsWithPositivePlayerCount: enrichedRooms.filter(r => r.player_count > 0).length,
            shouldBeVisibleToFresh2: 'Check if fresh2 can see correct player counts for rooms hosted by fresh'
          }
        });
        
        setGameRooms(enrichedRooms as unknown as GameRoom[]); // Ensure GameRoom type matches
        
        if (enrichedRooms.length === 0 && fetchedRooms && fetchedRooms.length > 0) {
          console.warn('[LOBBY_FETCH] *** DATA TRANSFORMATION WARNING *** Enriched rooms is empty, but raw DB data was not!', { 
            rawDataCount: fetchedRooms.length,
            enrichedRoomsCount: enrichedRooms.length,
            rawDataSample: fetchedRooms[0],
            possibleIssue: 'Data transformation problem'
          });
        }
        setLobbyFetchError(false); // Clear error state on success
      }
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error('[LOBBY_FETCH] *** EXCEPTION *** Exception fetching game_rooms:', {
        exceptionMessage: error.message,
        exceptionName: error.name,
        exceptionStack: error.stack,
        userContext: user?.id,
        timestamp: new Date().toISOString()
      });
      setErrorMp(`Exception fetching rooms: ${error.message}`);
      setLobbyFetchError(true);
      setGameRooms([]);
    } finally {
      // Reset both ref and state to indicate loading is complete
      isLoadingRoomsRef.current = false;
      setIsLoadingRooms(false);
      console.log('[LOBBY_FETCH] *** CRITICAL DEBUG END *** fetchAndSetGameRooms FINISHED for user:', {
        userId: user?.id,
        timestamp: new Date().toISOString(),
        finalState: 'isLoadingRooms set to false (both ref and state)'
      });
    }
  }, [user, isAuthLoading]); // CRITICAL FIX: Removed isLoadingRooms to prevent infinite loop
  
  // Store the function in a ref for stable reference
  useEffect(() => {
    fetchAndSetGameRoomsRef.current = fetchAndSetGameRooms;
  }, [fetchAndSetGameRooms]);



  // Handler for successful authentication from AuthModal
  const handleAuthSuccess = useCallback((context: 'multiplayer' | 'general') => {
    console.log(`[AUTH_SUCCESS] Authentication successful with context: ${context}`);
    
    // If user signed in with multiplayer context, automatically navigate to multiplayer
    if (context === 'multiplayer') {
      console.log('[AUTH_SUCCESS] Automatically switching to multiplayer mode after successful authentication');
      
      // CRITICAL FIX: Only set the state, do NOT fetch data here
      // The data fetching will be handled by the new useEffect below
      setSelectedOverallGameType('multiplayer');
      
      // ENHANCED: Ensure clean state when switching to multiplayer
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setActiveRoomId(null);
      setSelectedRoomForDetail(null);
      setCurrentRoomGameData(null);
      setPlayersInRoom([]);
      setErrorMp(null);
    }
  }, []); // Remove fetchAndSetGameRooms from dependencies since we're not calling it

  // NEW: Data-fetching useEffect to handle race condition
  // This effect will run whenever the user logs in/out or switches game modes
  useEffect(() => {
    console.log('[LOBBY_EFFECT] Checking if lobby data needs to be fetched.', {
      isMultiplayer: selectedOverallGameType === 'multiplayer',
      hasUser: !!user,
      isAuthLoading: isAuthLoading,
      isInRoom: !!activeRoomId
    });

    // CRITICAL FIX: Only fetch lobby data when NOT in a room
    if (selectedOverallGameType === 'multiplayer' && user && !isAuthLoading && !activeRoomId) {
      // Because this runs *after* the user state has updated, 'user' will be valid
      console.log('[LOBBY_EFFECT] Conditions met. Fetching game rooms for user:', user.id);
      fetchAndSetGameRooms();
    } else if (selectedOverallGameType === 'multiplayer' && !user) {
      // This case handles when a user is in the lobby and then signs out
      console.log('[LOBBY_EFFECT] In multiplayer view but user signed out. Clearing rooms.');
      setGameRooms([]); // Clear the lobby
    }
  }, [user, selectedOverallGameType, activeRoomId, isAuthLoading, fetchAndSetGameRooms]); // Added activeRoomId dependency

  // Update handleOverallGameTypeChange to remove data fetching
  const handleOverallGameTypeChange = useCallback((type: OverallGameType) => {
    console.log("handleOverallGameTypeChange called with:", type, "Current type:", selectedOverallGameType);
    
    if (type === 'multiplayer') {
      // *** THE CRUCIAL GUARD CLAUSE ***
      if (!user) {
        console.log('[AUTH_CHECK] User is not logged in. Opening auth modal to proceed to multiplayer.');
        authModalRef.current?.openAuthModal('multiplayer'); // Show the login/signup form with multiplayer context
        return; // Stop the function here
      }
      // If we get here, the user is logged in. Proceed.
      console.log('Switching to multiplayer mode');
      
      // ENHANCED: Ensure clean state when switching to multiplayer
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setActiveRoomId(null);
      setSelectedRoomForDetail(null);
      setCurrentRoomGameData(null);
      setPlayersInRoom([]);
      setErrorMp(null);
      isLoadingRoomsRef.current = false; // Reset ref
      setIsLoadingRooms(false); // Reset loading state when switching to multiplayer
      
      // REMOVED: Data fetching is now handled by the useEffect above
      // The useEffect will automatically fetch when user and selectedOverallGameType conditions are met
    } else {
      console.log("Switching to single-player mode");
      resetCurrentModeGame();
    }
    
    // Set the state last, after the checks
    setSelectedOverallGameType(type);
  }, [resetCurrentModeGame, user, selectedOverallGameType]); // Removed fetchAndSetGameRooms and isAuthLoading dependencies

  // Handler for clicking a recent answer
  const handleRecentSelect = (player: PlayerData) => {
    setSelectedPlayerInfo(player);
    setViewingRecentPlayer(player);
  };

  // Clear selected player info on next question or new game
  const handleNextQuestionClick = () => {
    setSelectedPlayerInfo(null);
    setViewingRecentPlayer(null);
    nextQuestion();
  };

  // Handler for returning to game from recent player view
  const handleReturnToGame = () => {
    setViewingRecentPlayer(null);
    const { isAnswered: currentIsAnswered, currentQuestion: gameCurrentQuestion } = useGameStore.getState();
    if (currentIsAnswered && gameCurrentQuestion && gameCurrentQuestion.correctPlayer) {
        setSelectedPlayerInfo(gameCurrentQuestion.correctPlayer);
    } else {
        setSelectedPlayerInfo(null);
    }
  };

  // Handler for resetting the game
  const handleGameResetClick = () => {
    setSelectedPlayerInfo(null);
    setViewingRecentPlayer(null);
    resetCurrentModeGame();
  };

  // Handler for switching game mode
  const handleModeButtonClick = (mode: GameModeType) => {
    setViewingRecentPlayer(null);
    setSelectedPlayerInfo(null);
    setGameMode(mode);
  };

  // **DEFINITIVE FIX**: Comprehensive State Sync Function
  // This function fetches ALL necessary data to render an active game room
  const syncFullRoomState = useCallback(async (roomId: string, caller: string) => {
    console.log(`[SYNC_STATE] *** STARTING FULL ROOM STATE SYNC *** for room ${roomId}, called by: ${caller}`);
    console.log(`[SYNC_STATE] This should ELIMINATE the "Waiting for game data..." issue`);
    
    // CRITICAL FIX: Skip sync if transition is in progress to prevent race conditions
    if (transitionInProgressRef.current) {
      console.log('[SYNC_STATE] SKIPPING sync - transition in progress to prevent race condition');
      return;
    }

    // Use Promise.all to fetch room details and players concurrently for performance
    console.log(`[SYNC_STATE] Fetching room details and players concurrently...`);
    const [roomResult, playersResult] = await Promise.all([
      supabase.from('game_rooms').select('*').eq('id', roomId).single(),
      supabase.from('game_players').select('*, profiles(username)').eq('room_id', roomId)
    ]);

    // 1. Check for errors in fetching the room itself
    if (roomResult.error) {
      console.error(`[SYNC_STATE] Failed to fetch room details for ${roomId}:`, roomResult.error);
      setErrorMp('Could not load game data.');
      setActiveRoomId(null); // Boot from the room
      return;
    }
    if (!roomResult.data) {
      console.error(`[SYNC_STATE] Room ${roomId} not found.`);
      setErrorMp('Game room no longer exists.');
      setActiveRoomId(null);
      return;
    }

    // 2. Check for errors in fetching players
    if (playersResult.error) {
      console.error(`[SYNC_STATE] Failed to fetch players for ${roomId}:`, playersResult.error);
      // You might still want to proceed with the room data you have
    }
    
    const roomData = roomResult.data;
    const playersData = playersResult.data || [];

    console.log('[SYNC_STATE] *** FETCHED DATA SUCCESS ***', {
      roomData: {
        id: roomData.id,
        status: roomData.status,
        host_id: roomData.host_id,
        current_question_data: !!roomData.current_question_data,
        player_scores: roomData.player_scores,
        current_round_number: roomData.current_round_number
      },
      playersData: {
        count: playersData.length,
        userIds: playersData.map(p => p.user_id),
        connectedCount: playersData.filter(p => p.is_connected).length
      },
      caller,
      timestamp: new Date().toISOString()
    });
    
    // 3. Transform players data (using existing logic)
    const transformedPlayers: PlayerInRoom[] = playersData.map(p => {
      // Handle the profiles data from the join
      let usernameFromProfile: string | null = null;
      if (p.profiles) {
        if (Array.isArray(p.profiles)) {
          usernameFromProfile = p.profiles[0]?.username || null;
        } else {
          usernameFromProfile = (p.profiles as { username: string | null }).username || null;
        }
      }

      return {
        user_id: p.user_id,
        is_ready: p.is_ready,
        is_connected: p.is_connected,
        profile: usernameFromProfile ? { username: usernameFromProfile } : null
      };
    });

    // 4. Set ALL relevant state in a batch to trigger a single re-render
    console.log('[SYNC_STATE] *** SETTING ALL GAME STATES ATOMICALLY ***');
    console.log('[SYNC_STATE] This is the MISSING PIECE that fixes the race condition');
    
    // **CRITICAL FIX**: Set currentRoomGameData FIRST - this was the missing piece!
    setCurrentRoomGameData(roomData as GameRoom);
    setPlayersInRoom(transformedPlayers);
    setActiveRoomId(roomData.id);
    
    // DO NOT update previousQuestionIdRef here - let the submission useEffect handle it
    // This was causing submission state to not reset on new questions
    if (roomData.current_question_data?.questionId) {
      const isNewQuestion = previousQuestionIdRef.current && previousQuestionIdRef.current !== roomData.current_question_data.questionId;
      console.log('[SYNC_STATE] New question detected in sync:', roomData.current_question_data.questionId, 'previous was:', previousQuestionIdRef.current);
      
      // CRITICAL FIX: Clear animation state when sync detects a new question
      if (isNewQuestion) {
        setEnhancedAnimatedAnswers({});
        // Note: triggeredAnimationsRef clearing will be handled by the component effect
        console.log('[SYNC_STATE] Cleared animation state for new question during sync');
      }
    }

    console.log('[SYNC_STATE] *** STATE SYNC COMPLETE ***', {
      roomId: roomData.id,
      roomStatus: roomData.status,
      hasCurrentQuestionData: !!roomData.current_question_data,
      currentQuestionId: roomData.current_question_data?.questionId,
      currentRoundNumber: roomData.current_round_number,
      answersCount: roomData.current_round_answers?.length || 0,
      playerCount: transformedPlayers.length,
      connectedPlayers: transformedPlayers.filter(p => p.is_connected).length,
      currentUser: user?.id,
      isCurrentUserInRoom: transformedPlayers.some(p => p.user_id === user?.id),
      caller,
      timestamp: new Date().toISOString()
    });

  }, [user?.id, setErrorMp, setActiveRoomId, setCurrentRoomGameData, setPlayersInRoom]);

  // Update ref when syncFullRoomState changes
  useEffect(() => {
    syncFullRoomStateRef.current = syncFullRoomState;
  }, [syncFullRoomState]);

  const handleJoinRoom = useCallback(async (roomToJoin: GameRoom | string, autoJoinedAfterCreate = false) => {
    // Handle both old API (roomId string) and new API (full room object)
    const roomId = typeof roomToJoin === 'string' ? roomToJoin : roomToJoin.id;
    const roomObject = typeof roomToJoin === 'string' ? null : roomToJoin;
    
    console.log(`[Client] *** HANDLEJOINROOM ENTRY *** handleJoinRoomAttempt called for room: ${roomId}. Current activeRoomId: ${activeRoomId}, User: ${user?.id}`);
    console.log(`[Client] *** CRITICAL DEBUG *** Room ID analysis:`, {
      roomIdParameter: roomId,
      roomIdType: typeof roomId,
      roomIdLength: roomId?.length,
      roomObjectProvided: !!roomObject,
      autoJoinedAfterCreate,
      currentActiveRoomId: activeRoomId,
      currentUserId: user?.id,
      timestamp: new Date().toISOString(),
      isCreatingAndJoiningRoom,
      isJoiningOrRejoiningRoom
    });

    if (!user?.id) {
      console.warn('[Client] Join attempt: user state missing. Verifying session directly.');
      try {
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        if (sessionError) {
          console.error('[Client] getSession error during join attempt:', sessionError);
        }
        if (sessionData?.session?.user) {
          console.log('[Client] Session user found. User state managed by AuthProvider, continuing join.');
          // User state is now managed by AuthProvider, no need to manually set
        } else {
          console.error('[Client] Join attempt: No user session found after verification.');
          setErrorMp("You must be logged in to join or rejoin a room.");
          return;
        }
      } catch (sessionException) {
        console.error('[Client] Exception verifying session during join attempt:', sessionException);
        setErrorMp("You must be logged in to join or rejoin a room.");
        return;
      }
    }

    // Prevent multiple simultaneous join attempts
    if (isJoiningOrRejoiningRoom) {
      console.warn('[Client] Join attempt already in progress, ignoring duplicate request.');
      return;
    }

    // Let React useEffect hooks manage subscription lifecycle cleanly
    console.log('[Client] Joining room - React useEffect hooks will handle subscription transitions');

    setIsJoiningOrRejoiningRoom(true);
    setErrorMp(null);
    
    if (activeRoomId && activeRoomId !== roomId) {
      // This implies user is trying to join a NEW room while already in another.
      console.warn(`[Client] Join attempt: User is already in activeRoomId ${activeRoomId}, attempting to join ${roomId}. This might need explicit leave first.`);
      // For now, we'll allow it and let the server-side logic handle it
    }

    try {
        console.log(`[Client] *** STARTING JOIN PROCESS *** User ${user?.id} attempting to join/rejoin room: ${roomId} via server-side logic.`);
        
        // **CRITICAL ENHANCEMENT**: Streamlined validation for host auto-join scenarios
        if (autoJoinedAfterCreate && isCreatingAndJoiningRoom) {
            console.log(`[Client] *** HOST AUTO-JOIN DETECTED *** This is a host auto-joining their newly created room. Skipping client-side player existence checks.`);
                            console.log(`[Client] *** HOST AUTO-JOIN CONTEXT ***`, {
                roomId,
                hostUserId: user?.id,
                autoJoinedAfterCreate,
                isCreatingAndJoiningRoom,
                rationale: "Host should not exist in game_players yet for this brand new room",
                timestamp: new Date().toISOString()
            });
        } else {
            console.log(`[Client] *** STANDARD JOIN/REJOIN *** Performing full validation checks.`);
            
            // Check if already connected client-side to this exact room
            if (activeRoomId === roomId && playersInRoom.some(p => p.user_id === user?.id)) {
                console.log(`[Client] User ${user?.id} is ALREADY CONNECTED client-side to room ${roomId}. Proceeding to room view if not already there.`);
                setMultiplayerPanelState('in_room');
                setCenterPanelMpState('mp_game_active');
                setIsJoiningOrRejoiningRoom(false);
                return;
            }
        }

        // Fetch room details for validation (always needed)
        console.log(`[Client] *** STEP 1: ROOM VALIDATION *** Fetching room details for validation`);

        const { data: roomValidationData, error: roomValidationError } = await supabase
            .from('game_rooms')
            .select('id, status, host_id, max_players, title')
            .eq('id', roomId)
            .maybeSingle();

        if (roomValidationError) {
            console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Error fetching room details for ${roomId}:`, roomValidationError);
            setErrorMp(`Room validation failed: ${roomValidationError.message}`);
            setIsJoiningOrRejoiningRoom(false);
            return;
        }

        if (!roomValidationData) {
            console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Room ${roomId} not found`);
            console.log(`[Client] STALE ROOM DETECTED: Room not found, likely cleaned up by janitor. Refreshing lobby.`);
            setErrorMp("Oops! That game is no longer available. The lobby has been refreshed.");

            // Auto-refresh the lobby to remove stale rooms
            setTimeout(async () => {
                console.log(`[Client] Auto-refreshing lobby after room not found`);
                await fetchAndSetGameRooms();
                // Clear the error after refresh
                setTimeout(() => setErrorMp(null), 3000);
            }, 1000);

            setIsJoiningOrRejoiningRoom(false);
            return;
        }

        console.log(`[Client] STEP 1 COMPLETE: Room validation successful:`, roomValidationData);

        // **CRITICAL ENHANCEMENT**: Different logic paths for host auto-join vs. standard join
        if (autoJoinedAfterCreate && isCreatingAndJoiningRoom && roomValidationData.host_id === user?.id) {
            console.log(`[Client] *** HOST AUTO-JOIN PATH *** Confirmed host auto-joining their own newly created room`);
            console.log(`[Client] *** STEP 2 (HOST AUTO-JOIN): SKIP PLAYER EXISTENCE CHECK *** For brand new room, host should not exist in game_players yet`);
            
            // For host auto-join, we skip the player existence check and go straight to INSERT
            // because this is a brand new room and the host should not exist in game_players yet
            console.log(`[Client] *** STEP 3 (HOST AUTO-JOIN): DIRECT INSERT *** Proceeding with host INSERT to game_players`);
            
        } else {
            console.log(`[Client] *** STANDARD JOIN PATH *** Performing full player existence validation`);
            
            // **STEP 2**: Check if player already exists in the room (for standard joins)
            console.log(`[Client] *** STEP 2: PLAYER EXISTENCE CHECK *** Checking if player ${user?.id} is already in game_players for room ${roomId}`);
            
            const { data: existingPlayerData, error: existingPlayerError } = await supabase
                .from('game_players')
                .select('user_id, is_connected, is_ready')
                .eq('room_id', roomId)
                .eq('user_id', user?.id)
                .maybeSingle();

            if (existingPlayerError) {
                console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Error checking existing player for room ${roomId}:`, existingPlayerError);
                setErrorMp(`Player validation failed: ${existingPlayerError.message}`);
                setIsJoiningOrRejoiningRoom(false);
                return;
            }

            if (existingPlayerData) {
                // Player EXISTS - this is a REJOIN scenario
                console.log(`[Client] STEP 2 COMPLETE: Player ${user?.id} EXISTS in game_players for room ${roomId}. This is a REJOIN.`, existingPlayerData);
                console.log(`[Client] *** REJOIN VALIDATION *** Checking if rejoin is allowed based on room status: ${roomValidationData.status}`);
                
                // For rejoins, we allow both 'waiting' and 'active' rooms (since user was already a player)
                if (roomValidationData.status === 'finished') {
                    console.error(`[Client] REJOIN VALIDATION: FAILED - Cannot rejoin finished game`);
                    setErrorMp(`Cannot rejoin: Game has finished.`);
                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }
                
                if (existingPlayerData.is_connected) {
                    console.log(`[Client] Player ${user?.id} is already marked as connected to room ${roomId}. Setting client state to match.`);
                } else {
                    console.log(`[Client] Player ${user?.id} exists but is disconnected. Updating to connected for reconnection.`);

                    // Enhanced reconnection with last_seen_at update
                    const { error: updateError } = await supabase
                        .from('game_players')
                        .update({
                            is_connected: true,
                            last_seen_at: new Date().toISOString()
                        })
                        .eq('room_id', roomId)
                        .eq('user_id', user?.id);

                    if (updateError) {
                        console.error(`[Client] Failed to update connection status for rejoin:`, updateError);
                        setErrorMp(`Failed to rejoin room: ${updateError.message}`);
                        setIsJoiningOrRejoiningRoom(false);
                        return;
                    }

                    console.log(`[Client] Successfully updated connection status and last_seen_at for reconnection`);
                }
                
                // *** DEFINITIVE FIX: Use comprehensive state sync instead of fragmented updates ***
                console.log(`[Client] *** USING COMPREHENSIVE STATE SYNC for rejoin ***`);
                await syncFullRoomState(roomId, 'rejoin_success');
                
                setMultiplayerPanelState('in_room');
                setCenterPanelMpState('mp_game_active');
                setSelectedRoomForDetail(null);
                
                if (autoJoinedAfterCreate) {
                    await fetchAndSetGameRooms(); // Refresh lobby list to show the new room
                }
                
                setIsJoiningOrRejoiningRoom(false);
                return;
            } else {
                // Player does NOT exist - this is a NEW JOIN scenario
                console.log(`[Client] STEP 2 COMPLETE: Player ${user?.id} does NOT exist in game_players for room ${roomId}. This is a NEW JOIN.`);
                
                // **STEP 3**: Additional validation for new joins
                console.log(`[Client] *** STEP 3: NEW JOIN VALIDATION *** Performing additional checks for new join`);
                
                // Check if room status is still joinable
                console.log(`[Client] *** STEP 3.1: STATUS VALIDATION *** Checking room status for new join. Current status: ${roomValidationData.status}`);
                if (roomValidationData.status !== 'waiting') {
                    console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Room ${roomId} is no longer in 'waiting' status. Current status: ${roomValidationData.status}`);

                    // Enhanced error handling for different room states
                    let errorMessage: string;
                    if (roomValidationData.status === 'active') {
                        // Check if this user was an original player (for potential rejoin)
                        const { data: originalPlayerData, error: originalPlayerError } = await supabase
                            .from('game_rooms')
                            .select('original_player_ids')
                            .eq('id', roomId)
                            .maybeSingle();

                        if (!originalPlayerError && originalPlayerData?.original_player_ids?.includes(user?.id)) {
                            errorMessage = `Game is already active, but you are an original player. Try rejoining instead of joining as new.`;
                            console.log(`[Client] STATUS VALIDATION: User ${user?.id} is an original player of active room ${roomId}. Suggesting rejoin.`);
                        } else {
                            errorMessage = `Cannot join: Game is already active and you are not an original player.`;
                            console.log(`[Client] STATUS VALIDATION: User ${user?.id} is NOT an original player of active room ${roomId}.`);
                        }
                    } else if (roomValidationData.status === 'finished') {
                        errorMessage = `Cannot join: Game has finished.`;
                    } else {
                        errorMessage = `Cannot join: Room status is '${roomValidationData.status}'. Only waiting rooms can be joined.`;
                    }

                    // Show user-friendly error and auto-refresh lobby
                    console.log(`[Client] STALE ROOM DETECTED: Showing user-friendly error and refreshing lobby`);
                    setErrorMp(`Oops! That game is no longer available. The lobby has been refreshed.`);

                    // Auto-refresh the lobby to remove stale rooms
                    setTimeout(async () => {
                        console.log(`[Client] Auto-refreshing lobby after stale room detection`);
                        await fetchAndSetGameRooms();
                        // Clear the error after refresh
                        setTimeout(() => setErrorMp(null), 3000);
                    }, 1000);

                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }

                // Check room capacity
                const { count: validationConnectedCount, error: validationCountError } = await supabase
                    .from('game_players')
                    .select('*', { count: 'exact', head: true })
                    .eq('room_id', roomId)
                    .eq('is_connected', true);

                if (validationCountError) {
                    console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Error counting connected players for room ${roomId}:`, validationCountError);
                    setErrorMp(`Room validation failed: ${validationCountError.message}`);
                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }

                const currentCount = validationConnectedCount || 0;
                const maxPlayers = roomValidationData.max_players || 8;
                if (currentCount >= maxPlayers) {
                    console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Room ${roomId} is at capacity. Players: ${currentCount}/${maxPlayers}`);
                    setErrorMp(`Cannot join: Room is full (${currentCount}/${maxPlayers} players).`);
                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }
                
                console.log(`[Client] STEP 3 COMPLETE: New join validation successful. Room has capacity: ${currentCount}/${maxPlayers}`);
            }
        }

        // **STEP 4**: Perform the INSERT for new joins (both host auto-join and standard new joins reach here)
        console.log(`[Client] *** STEP 4: INSERT ATTEMPT *** Attempting INSERT into game_players`);
        
        const insertData = {
            room_id: roomId,
            user_id: user?.id,
            is_connected: true,
            is_ready: false,
            last_seen_at: new Date().toISOString()
        };
        
        console.log(`[Client] VALIDATED INSERT: Attempting INSERT into game_players with validated data:`, insertData);
        
        const { data: insertedPlayerData, error: insertError } = await supabase
            .from('game_players')
            .insert(insertData)
            .select('user_id, room_id, is_connected, is_ready')
            .single();

        if (insertError) {
            console.error(`[Client] *** CRITICAL ERROR *** INSERT into game_players FAILED:`, {
                error: insertError,
                message: insertError.message,
                code: insertError.code,
                details: insertError.details,
                hint: insertError.hint,
                insertData,
                roomId,
                userId: user?.id,
                autoJoinedAfterCreate,
                isCreatingAndJoiningRoom,
                timestamp: new Date().toISOString()
            });

            // Special handling for duplicate key errors (409 conflicts)
            if (insertError.code === '23505' || insertError.message?.includes('duplicate key')) {
                console.error(`[Client] *** DUPLICATE KEY VIOLATION *** This suggests a race condition or the user already exists in the room:`, {
                    possibleCauses: [
                        "Race condition: Multiple join attempts for the same user",
                        "User already exists but client-side check missed it",
                        "Previous join attempt left orphaned record"
                    ],
                    roomId,
                    userId: user?.id,
                    autoJoinedAfterCreate,
                    currentTimestamp: new Date().toISOString()
                });
                setErrorMp("Join failed: You may already be in this room or there was a timing conflict. Please refresh and try again.");
            } else {
                setErrorMp(`Failed to join room: ${insertError.message}`);
            }

            // THE FIX: If join fails, we need to re-establish global subscriptions
            // since we nuked them at the beginning of handleJoinRoom
            console.log('[Client] [JOIN_FAILED] Re-establishing global subscriptions after failed join attempt');
            // The global subscriptions useEffect will automatically run when activeRoomId is null
            // and user is authenticated, so we don't need to do anything explicit here

            setIsJoiningOrRejoiningRoom(false);
            return;
        }

        console.log(`[Client] Step 4 complete: User ${user?.id} successfully made NEW JOIN (inserted) to game_players for room ${roomId}. Data:`, insertedPlayerData);

        // *** DEFINITIVE FIX: Use comprehensive state sync instead of fragmented updates ***
        console.log(`[Client] *** USING COMPREHENSIVE STATE SYNC for new join ***`);
        await syncFullRoomState(roomId, 'new_join_success');
        
        setMultiplayerPanelState('in_room');
        setCenterPanelMpState('mp_game_active');
        setSelectedRoomForDetail(null);

        // Reset connection status to initializing when joining a room
        setConnectionStatus('INITIALIZING');
        
        console.log(`[Client] Client active state set for room ${roomId}. Awaiting data fetches and UI transition.`);
        
        if (autoJoinedAfterCreate) {
            await fetchAndSetGameRooms(); // Refresh lobby list to show the new room
        }

        await fetchAndSetGameRooms(); // Refresh lobby in case player counts changed
        setIsJoiningOrRejoiningRoom(false);

    } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error(`[Client] *** EXCEPTION *** Error in handleJoinRoomAttempt for room ${roomId}:`, {
            error: err,
            message: err.message,
            stack: err.stack,
            roomId,
            userId: user?.id,
            autoJoinedAfterCreate,
            isCreatingAndJoiningRoom,
            timestamp: new Date().toISOString()
        });
        setErrorMp(`Failed to join/rejoin room (exception): ${err.message}`);
        setIsJoiningOrRejoiningRoom(false);
    }
  }, [
    user?.id,
    activeRoomId,
    playersInRoom,
    isJoiningOrRejoiningRoom,
    isCreatingAndJoiningRoom,
    fetchAndSetGameRooms,
    syncFullRoomState
  ]);

  const handleViewLobbyDetail = (room: GameRoom) => {
    console.log("[LobbyDetail] Viewing room:", room.id, "Current gameRooms:", gameRooms);
    setSelectedRoomForDetail(room);
    setCenterPanelMpState('lobby_list_detail');
    setExpandedLeaderboardData(null);
    setActiveRoomId(null);
    setMultiplayerPanelState('lobby_list');
  };

  const handleExpandLeaderboard = (title: string, entries: LeaderboardEntry[]) => {
    setExpandedLeaderboardData({ title, entries });
    setCenterPanelMpState('expanded_leaderboard');
    setSelectedRoomForDetail(null);
    setActiveRoomId(null);
  };

  // Update handleLeaveRoom to use the Edge Function
  const handleLeaveRoom = async () => {
    if (!activeRoomId || !user) {
      console.warn("[Client] Cannot leave room: no room or user.");
      setErrorMp("Cannot leave room: missing room or user information.");
      return;
    }

    // 💡 THE CRITICAL GUARD CLAUSE - Prevent React Strict Mode double-invocation
    if (isLeavingRoom) {
      console.warn('[Client] Leave action already in progress. Ignoring duplicate call.');
      return;
    }

    console.log(`[Client] [LEAVE_ROOM] User ${user.id} attempting to leave room ${activeRoomId}`);
    setErrorMp(null); // Clear any previous errors

    try {
      // 1. Set the flag to true IMMEDIATELY
      setIsLeavingRoom(true);
      // Call the leave-room-handler Edge Function
      console.log(`[Client] [LEAVE_ROOM] Invoking leave-room-handler for room ${activeRoomId}`);
      const { data, error: invokeError } = await supabase.functions.invoke('leave-room-handler', {
        body: { roomId: activeRoomId },
      });

      if (invokeError) {
        console.error("[Client] Error invoking leave-room-handler:", invokeError);
        let errorMessage = 'Failed to leave room.';
        
        if (invokeError.context && invokeError.context.json) {
          const errorData = invokeError.context.json;
          errorMessage = errorData.error || errorData.message || invokeError.message;
        } else {
          errorMessage = invokeError.message || 'Unknown server error';
        }
        
        console.error("[Client] [LEAVE_ROOM_ERROR] Setting error message:", errorMessage);
        setErrorMp(errorMessage);
        return;
      }

      console.log("[Client] [LEAVE_ROOM_SUCCESS] Successfully left room:", data);
      
      // Update UI state immediately
      setActiveRoomId(null);
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setPlayersInRoom([]);
      setCurrentRoomGameData(null);
      
      // Clear any room-specific errors
      if (errorMp) {
        setErrorMp(null);
      }
      
      // Refresh the game rooms list to reflect changes
      console.log("[Client] [LEAVE_ROOM] Refreshing game rooms list...");
      await fetchAndSetGameRooms();
      
      // Show success message
      const roomDeleted = data.roomDeleted;
      const remainingPlayers = data.remainingPlayersCount;
      
      if (roomDeleted) {
        console.log(`[Client] [LEAVE_ROOM] Room was deleted (was empty)`);
        // Optional: Show toast or notification
      } else {
        console.log(`[Client] [LEAVE_ROOM] Room kept with ${remainingPlayers} remaining players`);
      }

    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error("[Client] Client-side exception leaving room:", error);
      setErrorMp(`Client-side exception: ${error.message}`);
    } finally {
      // 4. IMPORTANT: Unset the flag in a finally block
      // This ensures it gets reset even if an error occurs.
      setIsLeavingRoom(false);
    }
  };

  // Determine which player info to show in the right panel
  const playerToShow = viewingRecentPlayer 
                        ? viewingRecentPlayer 
                        : (selectedPlayerInfo ? selectedPlayerInfo : (isAnswered && currentQuestion ? currentQuestion.correctPlayer : null));

  // Format timer for display
  const formattedTimer = `${String(Math.floor(timer / 60)).padStart(1, '0')}:${String(timer % 60).padStart(2, '0')}`;

  // Update the roomCode handling useEffect to use fetchAndSetGameRooms
  useEffect(() => {
    const roomCodeFromUrl = clientSearchParams?.get('roomCode');
    console.log("[DEBUG] Detected roomCodeFromUrl:", roomCodeFromUrl, "isCreatingAndJoiningRoom:", isCreatingAndJoiningRoom);

    // **CRITICAL FIX**: Don't process URL room codes during room creation to prevent race conditions
          if (roomCodeFromUrl && !isAuthLoading && !isCreatingAndJoiningRoom) {
      if (!user) {
        setErrorMp("Please log in to join the room.");
        return;
      }

      if (selectedOverallGameType !== 'multiplayer') {
        handleOverallGameTypeChange('multiplayer');
      }
      
      const joinRoomByCode = async (code: string) => {
        setIsLoadingRooms(true);
        setErrorMp(null);
        try {
          const { data: roomData, error: roomError } = await supabase
            .from('game_rooms')
            .select('id, status, title, multiplayer_mode, profiles:host_id (username), created_at, host_id')
            .eq('room_code', code)
            .maybeSingle();

          if (roomError) throw roomError;

          if (roomData) {
            if (roomData.status === 'waiting') {
              await handleJoinRoom(roomData.id);
              // Refresh lobby after joining
              await fetchAndSetGameRooms();
            } else {
              setErrorMp(`Room '${roomData.title || code}' is no longer waiting or has already started.`);
              setSelectedRoomForDetail(roomData as unknown as GameRoom);
              setCenterPanelMpState('lobby_list_detail');
            }
          } else {
            setErrorMp(`Room with code "${code}" not found.`);
          }
        } catch (e) {
          const error = e instanceof Error ? e : new Error(String(e));
          console.error("[DEBUG] Error joining by room code:", error);
          setErrorMp(`Error finding or joining room: ${error.message}`);
        } finally {
          setIsLoadingRooms(false);
        }
      };

      if (multiplayerPanelState === 'lobby_list' || selectedOverallGameType === 'multiplayer') {
        joinRoomByCode(roomCodeFromUrl);
      }
    }
  }, [clientSearchParams, user, isAuthLoading, isCreatingAndJoiningRoom, handleOverallGameTypeChange, multiplayerPanelState, selectedOverallGameType, handleJoinRoom, fetchAndSetGameRooms]);

  // Update handleCreateRoom to include game settings
  const handleCreateRoom = async () => {
    console.log('[Client] [CREATE_ROOM] handleCreateRoom called');

    if (!user || !user.id) {
      console.error('[Client] Cannot create room: user not authenticated');
      setErrorMp('Cannot create room: user not authenticated.');
      return;
    }

    if (isCreatingRoom) {
      console.warn('[Client] Room creation already in progress, ignoring duplicate request');
      return;
    }

    console.log(`[Client] [CREATE_ROOM] User ${user.id} attempting to create room`);
    setIsCreatingRoom(true);
    setErrorMp(null); // Clear any previous errors
    
    try {
      // Step 1: Check for and clean up any existing rooms hosted by this user
      console.log(`[Client] [CREATE_ROOM] Checking for existing rooms hosted by user ${user.id}...`);
      const { data: existingHostRooms, error: fetchHostRoomsError } = await supabase
        .from('game_rooms')
        .select(`
          id,
          status,
          title,
          created_at,
          game_players (
            user_id,
            is_connected
          )
        `)
        .eq('host_id', user.id)
        .in('status', ['waiting', 'active']); // Only non-completed rooms

      if (fetchHostRoomsError) {
        console.error('[Client] [CREATE_ROOM] Error fetching existing host rooms:', fetchHostRoomsError);
        setErrorMp('Error checking for existing rooms. Please try again.');
        return;
      }

      if (existingHostRooms && existingHostRooms.length > 0) {
        console.log(`[Client] [CREATE_ROOM] Found ${existingHostRooms.length} existing rooms to analyze:`, existingHostRooms);

        // Show user-friendly message about cleanup
        const staleRooms = existingHostRooms; // ALL existing rooms are considered stale when host wants to create new one

        // Note: Previous logic was too restrictive - it would only clean up rooms where host was marked as disconnected
        // But if the host is trying to create a new room, they want to abandon ALL previous rooms

        if (staleRooms.length > 0) {
          console.log(`[Client] [CREATE_ROOM] Found ${staleRooms.length} stale rooms to clean up`);
          setErrorMp(`Cleaning up ${staleRooms.length} inactive room(s)...`);
        }

        // Clean up each existing room
        for (const oldRoom of existingHostRooms) {
          console.log(`[Client] [CREATE_ROOM] Analyzing room for cleanup: ${oldRoom.id} (${oldRoom.title || 'Untitled'})`);

          const hostPlayerRecord = oldRoom.game_players?.find(p => p.user_id === user.id);
          const connectedPlayers = oldRoom.game_players?.filter(p => p.is_connected) || [];
          const totalPlayers = oldRoom.game_players?.length || 0;

          // When a host tries to create a new game, they are signaling intent to abandon previous games.
          // A room should be cleaned up if:
          // 1. It's a waiting room (host hasn't committed to this game yet), OR
          // 2. It's an active room where the host is disconnected (host has left), OR
          // 3. ANY room where the host is trying to create a new room (they're abandoning old ones)
          //
          // CRITICAL FIX: If the host is trying to create a NEW game, they're done with ALL old games.
          // The is_connected flag might be stale, so we should treat ALL previous rooms as stale
          // when the host explicitly wants to create a new one.
          const hostIsDisconnected = !hostPlayerRecord || !hostPlayerRecord.is_connected;
          const isStaleRoom = true; // ALWAYS clean up old rooms when host wants to create new game

          console.log(`[Client] [CREATE_ROOM] Room ${oldRoom.id} analysis:`, {
            status: oldRoom.status,
            hasHostPlayerRecord: !!hostPlayerRecord,
            hostIsConnected: hostPlayerRecord?.is_connected,
            hostIsDisconnected,
            totalPlayers,
            connectedPlayers: connectedPlayers.length,
            isStaleRoom,
            staleReason: 'host wants to create new game - abandoning ALL previous rooms'
          });

          if (isStaleRoom) {
            console.log(`[Client] [CREATE_ROOM] Processing room ${oldRoom.id} for host departure...`);

            try {
              if (oldRoom.status === 'waiting' || connectedPlayers.length === 0) {
                // For waiting rooms or rooms with no connected players, delete entirely
                console.log(`[Client] [CREATE_ROOM] Deleting empty/waiting room ${oldRoom.id}...`);

                const { error: deletePlayersError } = await supabase
                  .from('game_players')
                  .delete()
                  .eq('room_id', oldRoom.id);

                if (deletePlayersError) {
                  console.error(`[Client] [CREATE_ROOM] Error deleting players from room ${oldRoom.id}:`, deletePlayersError);
                } else {
                  console.log(`[Client] [CREATE_ROOM] ✅ Deleted all players from room ${oldRoom.id}`);
                }

                const { error: deleteRoomError } = await supabase
                  .from('game_rooms')
                  .delete()
                  .eq('id', oldRoom.id);

                if (deleteRoomError) {
                  console.error(`[Client] [CREATE_ROOM] Error deleting room ${oldRoom.id}:`, deleteRoomError);
                } else {
                  console.log(`[Client] [CREATE_ROOM] ✅ Successfully deleted room ${oldRoom.id}`);
                }

              } else {
                // For active rooms with other connected players, just remove the host
                console.log(`[Client] [CREATE_ROOM] Removing host from active room ${oldRoom.id} with ${connectedPlayers.length} other players...`);

                const { error: removeHostError } = await supabase
                  .from('game_players')
                  .delete()
                  .eq('room_id', oldRoom.id)
                  .eq('user_id', user.id);

                if (removeHostError) {
                  console.error(`[Client] [CREATE_ROOM] Error removing host from room ${oldRoom.id}:`, removeHostError);
                } else {
                  console.log(`[Client] [CREATE_ROOM] ✅ Removed host from room ${oldRoom.id}. Game continues with other players.`);
                }

                // Promote the first connected player to be the new host
                if (connectedPlayers.length > 0) {
                  const newHostId = connectedPlayers[0].user_id;
                  console.log(`[Client] [CREATE_ROOM] Promoting player ${newHostId} to host of room ${oldRoom.id}...`);

                  const { error: promoteHostError } = await supabase
                    .from('game_rooms')
                    .update({ host_id: newHostId })
                    .eq('id', oldRoom.id);

                  if (promoteHostError) {
                    console.error(`[Client] [CREATE_ROOM] Error promoting new host for room ${oldRoom.id}:`, promoteHostError);
                  } else {
                    console.log(`[Client] [CREATE_ROOM] ✅ Promoted player ${newHostId} to host of room ${oldRoom.id}`);
                  }
                }
              }

            } catch (cleanupException) {
              console.error(`[Client] [CREATE_ROOM] Exception processing room ${oldRoom.id}:`, cleanupException);
              // Continue with other rooms
            }
          } 
          // Note: else case removed since isStaleRoom is now always true
        }

        // Refresh the lobby to reflect changes
        console.log(`[Client] [CREATE_ROOM] Refreshing lobby after cleanup...`);
        await fetchAndSetGameRooms();

        // Clear the cleanup message
        setErrorMp(null);
      } else {
        console.log(`[Client] [CREATE_ROOM] No existing rooms found for user ${user.id}`);
      }

      // Step 2: Create the new room
      console.log(`[Client] [CREATE_ROOM] Creating new room...`);
      const roomTitle = `${userProfile?.username || 'Host'}'s Game`;
      const now = new Date().toISOString();
      const newRoomData = {
        host_id: user.id,
        title: roomTitle,
        status: 'waiting' as const,
        multiplayer_mode: 'competitive' as const,
        max_players: 4,
        created_at: now,
        last_activity_timestamp: now, // Initialize activity timestamp for janitor tracking
      };

      console.log('[Client] [CREATE_ROOM] Inserting new room with data:', newRoomData);
      const { data: newRoom, error: createError } = await supabase
        .from('game_rooms')
        .insert(newRoomData)
        .select()
        .single();

      if (createError) {
        console.error('[Client] [CREATE_ROOM] Error creating room:', createError);
        setErrorMp(`Failed to create room: ${createError.message}`);
        return;
      }

      if (!newRoom) {
        console.error('[Client] [CREATE_ROOM] No room data returned from insert');
        setErrorMp('Failed to create room: no data returned.');
        return;
      }

      console.log(`[Client] [CREATE_ROOM] *** ROOM CREATED SUCCESSFULLY *** ${JSON.stringify(newRoom)}`);
      const newRoomId = newRoom.id;

      // Step 3: Auto-join the host to their own room
      console.log(`[Client] [CREATE_ROOM] *** ABOUT TO CALL HANDLEJOINROOM *** with room ID: ${newRoomId}`);
      await handleJoinRoom(newRoomId, true); // true indicates auto-join after create

      console.log(`[Client] [CREATE_ROOM] *** AUTO-JOIN COMPLETED SUCCESSFULLY ***`);

      // THE FIX: Immediately fetch the player list for the new room so the host sees themselves
      console.log('[Client] [CREATE_ROOM] Fetching initial player list for host after auto-join');
      await fetchPlayersInActiveRoom(newRoomId, 'host_create');

      console.log(`[Client] [CREATE_ROOM] *** ROOM CREATION AND HOST AUTO-JOIN FULLY COMPLETED *** Room: ${newRoomId}`);

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      console.error('[Client] [CREATE_ROOM] Exception during room creation:', err);
      setErrorMp(`Exception creating room: ${err.message}`);
    } finally {
      // IMPORTANT: Unset the flag in a finally block
      // This ensures it gets reset even if an error occurs.
      setIsCreatingRoom(false);
    }
  };

  // Add fetchPlayersInActiveRoom function
  const fetchPlayersInActiveRoom = useCallback(async (roomId: string, caller: string = 'unknown') => {
    const currentRoomDetails = gameRooms.find(room => room.id === roomId);
    const isCurrentUserHost = currentRoomDetails?.host_id === user?.id;
    const logPrefix = isCurrentUserHost ? '[RoomView HOST]' : '[RoomView]';
    
    console.log(`${logPrefix} [FETCH TRIGGERED] fetchPlayersInActiveRoom called:`, {
      roomId,
      caller,
      timestamp: new Date().toISOString(),
      currentPlayersCount: playersInRoom.length,
      stackTrace: new Error().stack?.split('\n').slice(1, 4).join(' | '), // Show top 3 stack frames
      triggerContext: {
        isCurrentUserHost,
        userId: user?.id,
        hostId: currentRoomDetails?.host_id,
        currentPlayerIds: playersInRoom.map(p => p.user_id),
        multiplayerPanelState,
        centerPanelMpState
      }
    });

    if (!roomId) {
      console.warn(`${logPrefix} fetchPlayersInActiveRoom: No roomId provided, returning early`);
      return;
    }

    // Only show loading state for initial loads or manual refreshes
    // Never show loading for realtime updates or host polling to avoid UI blinking
    const shouldShowLoading = playersInRoom.length === 0 || caller === "manual_refresh";
    const noLoadingCallers = ['realtime_player_update', 'host_poll'];
    const actuallyShowingLoading = shouldShowLoading && !noLoadingCallers.includes(caller);
    
    // Set loading state only if we should actually show it
    if (actuallyShowingLoading) {
      setIsLoadingPlayers(true);
    }
    
    console.log(`${logPrefix} Starting to fetch players for room: ${roomId} (called by: ${caller}, showLoading: ${actuallyShowingLoading})`);
    
    try {
      console.log(`${logPrefix} Executing Supabase query for game_players WITH profiles join (restored after RLS fix)`);
      const { data, error } = await supabase
        .from('game_players')
        .select(`
          user_id,
          is_ready,
          is_connected,
          room_id,
          profiles (
            username
          )
        `)
        .eq('room_id', roomId);

      if (error) {
        console.error(`${logPrefix} Database error fetching players (called by: ${caller}):`, {
          error: JSON.stringify(error, null, 2),
          errorMessage: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          roomId,
          caller
        });
        setPlayersInRoom([]);
        setErrorMp(`Failed to fetch players: ${error.message}`);
      } else {
        console.log(`${logPrefix} Successfully fetched players from database (called by: ${caller}):`, {
          rawData: data,
          playerCount: data?.length || 0,
          roomId,
          caller,
          timestamp: new Date().toISOString()
        });

        // Transform the data to match PlayerInRoom type
        const transformedData: PlayerInRoom[] = (data || []).map((dbPlayer, index) => {
          console.log(`${logPrefix} Transforming player ${index + 1} (WITH profile join, called by: ${caller}):`, {
            rawPlayer: dbPlayer,
            hasProfileData: !!dbPlayer.profiles,
            profileData: dbPlayer.profiles
          });

          // Handle the profiles data from the join
          let usernameFromProfile: string | null = null;
          if (dbPlayer.profiles) {
            if (Array.isArray(dbPlayer.profiles)) {
              // If Supabase returns profiles as an array
              usernameFromProfile = dbPlayer.profiles[0]?.username || null;
              console.log(`${logPrefix} Profile is array, extracted username:`, usernameFromProfile);
            } else {
              // If Supabase returns profiles as an object
              usernameFromProfile = (dbPlayer.profiles as { username: string | null }).username || null;
              console.log(`${logPrefix} Profile is object, extracted username:`, usernameFromProfile);
            }
          } else {
            console.log(`${logPrefix} No profile data for player:`, dbPlayer.user_id);
          }

          const transformedPlayer = {
            user_id: dbPlayer.user_id,
            is_ready: dbPlayer.is_ready,
            is_connected: dbPlayer.is_connected,
            profile: usernameFromProfile ? { username: usernameFromProfile } : null
          };

          console.log(`${logPrefix} Transformed player ${index + 1} (with profile):`, transformedPlayer);
          return transformedPlayer;
        });

        console.log(`${logPrefix} Final transformed players data (called by: ${caller}):`, {
          transformedData,
          playerCount: transformedData.length,
          playersWithUsernames: transformedData.filter(p => p.profile?.username).length,
          playersReady: transformedData.filter(p => p.is_ready).length,
          playersConnected: transformedData.filter(p => p.is_connected).length,
          caller
        });

        // **CRITICAL DEBUG LOGGING** - Log exact data about to be set in state
        console.log(`${logPrefix} [CRITICAL] *** DATA ABOUT TO BE SET BY fetchPlayersInActiveRoom (called by: ${caller}) ***`, {
          roomId,
          isCurrentUserHost,
          currentUserId: user?.id,
          hostId: currentRoomDetails?.host_id,
          caller,
          dataToSet: {
            playerCount: transformedData.length,
            playersDetailed: transformedData.map(p => ({
              user_id: p.user_id,
              username: p.profile?.username || 'NO_USERNAME',
              is_ready: p.is_ready,
              is_connected: p.is_connected,
              hasProfile: !!p.profile
            })),
            userIdsList: transformedData.map(p => p.user_id),
            currentUserInList: transformedData.some(p => p.user_id === user?.id),
            uniqueUserIds: [...new Set(transformedData.map(p => p.user_id))].length === transformedData.length
          },
          previousState: {
            currentPlayersInRoomCount: playersInRoom.length,
            currentPlayerIds: playersInRoom.map(p => p.user_id)
          },
          expectUIToUpdate: transformedData.length !== playersInRoom.length,
          timestamp: new Date().toISOString()
        });

        if (isCurrentUserHost && transformedData.length > 1) {
          console.log(`${logPrefix} [HOST CRITICAL] *** HOST SETTING STATE WITH MULTIPLE PLAYERS (called by: ${caller}) *** This should update host UI:`, {
            caller,
            totalPlayersToSet: transformedData.length,
            hostShouldSeeThesePlayerIds: transformedData.map(p => p.user_id),
            hostShouldSeeTheseUsernames: transformedData.map(p => p.profile?.username || 'NO_USERNAME'),
            UIUpdateExpected: true,
            playerCountShouldChangeTo: transformedData.length,
            previousPlayerCount: playersInRoom.length,
            whatTriggeredThisFetch: caller
          });
        }

        // **CRITICAL: Create a new array instance to ensure React detects the change**
        const dataToSet = [...transformedData]; // Explicitly create new array
        console.log(`${logPrefix} [IMMUTABILITY CHECK] Creating new array instance for setPlayersInRoom (called by: ${caller}):`, {
          originalArrayReference: transformedData,
          newArrayReference: dataToSet,
          isNewInstance: dataToSet !== transformedData,
          arrayContentsMatch: JSON.stringify(dataToSet) === JSON.stringify(transformedData),
          caller,
          timestamp: new Date().toISOString()
        });

        // **ENHANCED: Log the exact data being passed to setPlayersInRoom**
        console.log(`${logPrefix} [SET_STATE] About to call setPlayersInRoom with (called by: ${caller}):`, {
          dataBeingSet: JSON.parse(JSON.stringify(dataToSet)), // Deep copy for logging
          playerCount: dataToSet.length,
          playerUserIds: dataToSet.map(p => p.user_id),
          playerUsernames: dataToSet.map(p => p.profile?.username || 'NO_USERNAME'),
          isCurrentUserHost,
          roomId,
          caller,
          timestamp: new Date().toISOString()
        });

        setPlayersInRoom(dataToSet);
        console.log(`${logPrefix} Updated playersInRoom state successfully (called by: ${caller})`);
      }
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error(`${logPrefix} Exception during fetchPlayersInActiveRoom (called by: ${caller}):`, {
        exception: error,
        exceptionMessage: error.message,
        exceptionStack: error.stack,
        roomId,
        caller
      });
      setPlayersInRoom([]);
      setErrorMp(`Exception fetching players: ${error.message}`);
    } finally {
      // Only reset loading state if we actually set it
      if (actuallyShowingLoading) {
        setIsLoadingPlayers(false);
      }
      console.log(`${logPrefix} fetchPlayersInActiveRoom completed (called by: ${caller}):`, {
        roomId,
        caller,
        timestamp: new Date().toISOString(),
        finalPlayersCount: playersInRoom.length,
        wasLoadingShown: actuallyShowingLoading
      });
    }
  }, [user?.id]); // Only depend on user ID which is stable
  
  // Store the function in a ref for stable reference
  useEffect(() => {
    fetchPlayersInActiveRoomRef.current = fetchPlayersInActiveRoom;
  }, [fetchPlayersInActiveRoom]);



  // Add handleToggleReady function with optimistic updates and submission state
  const handleToggleReady = async () => {
    console.log('[RoomView] handleToggleReady called - Starting comprehensive logging');
    console.log('[RoomView] Current state check:', {
      hasUser: !!user,
      userId: user?.id,
      hasActiveRoomId: !!activeRoomId,
      activeRoomId,
      isSubmittingReady,
      playersInRoomCount: playersInRoom.length,
      timestamp: new Date().toISOString()
    });

    // Early exit conditions with detailed logging
    if (!user?.id || !activeRoomId || isSubmittingReady) {
      if (isSubmittingReady) {
        console.log('[RoomView] Already submitting ready state, skipping this call to prevent duplicate submissions');
        return;
      }
      if (!user?.id) {
        console.warn('[RoomView] handleToggleReady: User ID missing, cannot proceed');
        return;
      }
      if (!activeRoomId) {
        console.warn('[RoomView] handleToggleReady: Active room ID missing, cannot proceed');
        return;
      }
      return;
    }

    // Find current player and validate
    const currentPlayerInRoom = playersInRoom.find(p => p.user_id === user.id);
    console.log('[RoomView] Current player lookup:', {
      foundPlayer: !!currentPlayerInRoom,
      currentPlayerData: currentPlayerInRoom,
      allPlayersInRoom: playersInRoom.map(p => ({ user_id: p.user_id, is_ready: p.is_ready, username: p.profile?.username }))
    });

    if (!currentPlayerInRoom) {
      console.error('[RoomView] Current player not found in playersInRoom list - this should not happen');
      console.error('[RoomView] Debug info:', {
        searchingForUserId: user.id,
        playersInRoom: playersInRoom,
        playersInRoomUserIds: playersInRoom.map(p => p.user_id)
      });
      return;
    }

    // Determine new ready state
    const currentReadyState = currentPlayerInRoom.is_ready;
    const newReadyState = !currentReadyState;
    
    console.log('[RoomView] Ready state transition:', {
      currentReadyState,
      newReadyState,
      playerUserId: user.id,
      roomId: activeRoomId
    });

    // Set submitting state to prevent multiple calls
    setIsSubmittingReady(true);
    console.log('[RoomView] Set isSubmittingReady to true - blocking further submissions');

    // 1. Optimistic UI Update
    console.log('[RoomView] Applying optimistic UI update');
    setPlayersInRoom(prevPlayers => {
      const updatedPlayers = prevPlayers.map(p =>
        p.user_id === user.id ? { ...p, is_ready: newReadyState } : p
      );
      console.log('[RoomView] Optimistic update applied:', {
        beforeUpdate: prevPlayers.find(p => p.user_id === user.id),
        afterUpdate: updatedPlayers.find(p => p.user_id === user.id),
        allPlayersAfterUpdate: updatedPlayers.map(p => ({ user_id: p.user_id, is_ready: p.is_ready }))
      });
      return updatedPlayers;
    });

    try {
      console.log(`[RoomView] Starting database update - attempting to update ready state to: ${newReadyState} for user ${user.id} in room ${activeRoomId}`);
      
      const { data: updatedGamePlayer, error } = await supabase
        .from('game_players')
        .update({ is_ready: newReadyState })
        .eq('user_id', user.id)
        .eq('room_id', activeRoomId)
        .select()
        .single();

      if (error) {
        console.error('[RoomView] Database update failed:', {
          error: JSON.stringify(error, null, 2),
          errorMessage: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          attemptedUpdate: { user_id: user.id, room_id: activeRoomId, is_ready: newReadyState }
        });
        
        // Revert optimistic update on error
        console.log('[RoomView] Reverting optimistic update due to database error');
        setPlayersInRoom(prevPlayers => {
          const revertedPlayers = prevPlayers.map(p =>
            p.user_id === user.id ? { ...p, is_ready: currentReadyState } : p
          );
          console.log('[RoomView] Optimistic update reverted:', {
            revertedTo: currentReadyState,
            playerAfterRevert: revertedPlayers.find(p => p.user_id === user.id)
          });
          return revertedPlayers;
        });
        
        setErrorMp(`Failed to update ready state: ${error.message}`);
      } else {
        console.log('[RoomView] Database update successful:', {
          updatedGamePlayer,
          confirmedReadyState: updatedGamePlayer?.is_ready,
          updateTimestamp: new Date().toISOString()
        });

        // ✅ ROBUST APPROACH: Trust the successful database write
        // The optimistic update is now the confirmed state. No need to wait for realtime.
        // Realtime's job is to inform us of OTHER users' actions, not our own.
        console.log('[RoomView] Database write succeeded - optimistic state is now confirmed state');

        // Clear any previous errors since the operation was successful
        setErrorMp(null);
      }
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error('[RoomView] Exception during ready state update:', {
        exception: error,
        exceptionMessage: error.message,
        exceptionStack: error.stack,
        attemptedUpdate: { user_id: user.id, room_id: activeRoomId, is_ready: newReadyState }
      });

      // Revert optimistic update on exception
      console.log('[RoomView] Reverting optimistic update due to exception');
      setPlayersInRoom(prevPlayers => {
        const revertedPlayers = prevPlayers.map(p =>
          p.user_id === user.id ? { ...p, is_ready: currentReadyState } : p
        );
        console.log('[RoomView] Optimistic update reverted after exception:', {
          revertedTo: currentReadyState,
          playerAfterRevert: revertedPlayers.find(p => p.user_id === user.id)
        });
        return revertedPlayers;
      });

      setErrorMp(`Exception updating ready state: ${error.message}`);
    } finally {
      setIsSubmittingReady(false);
      console.log('[RoomView] Set isSubmittingReady to false - ready for next submission');
      console.log('[RoomView] handleToggleReady completed at:', new Date().toISOString());
    }
  };

  // FINAL EFFECT 1: ISOLATED Realtime Subscription Management
  // This effect ONLY manages WebSocket connections with minimal dependencies
  useEffect(() => {
    console.log('[Realtime] Realtime subscription effect triggered', {
      hasUser: !!user?.id,
      activeRoomId,
      selectedOverallGameType
    });

    // If the user isn't logged in, ensure all channels are cleaned up
    if (!user?.id || !supabase) {
      console.log('[Realtime] No user or no supabase client - cleaning up all channels');
      supabase?.removeAllChannels();
      return;
    }

    let channel: RealtimeChannel;

    if (activeRoomId) {
      // ---- STATE 1: IN A GAME ROOM ----
      // Only cleanup non-room channels, preserve existing room channels
      const existingChannels = supabase.getChannels();
      existingChannels.forEach(ch => {
        if (!ch.topic.startsWith(`room-${activeRoomId}`)) {
          console.log('[Realtime] Removing non-room channel:', ch.topic);
          supabase.removeChannel(ch);
        }
      });
      
      // Check if we already have a channel for this room
      const existingRoomChannel = existingChannels.find(ch => ch.topic === `room-${activeRoomId}`);
      if (existingRoomChannel) {
        console.log('[Realtime] Room channel already exists, reusing:', activeRoomId);
        channel = existingRoomChannel;
        return; // Exit early - channel already set up
      } else {
        console.log('[Realtime] ✅ Setting up NEW subscriptions for room:', activeRoomId);
        
        // Create a single channel for room-specific updates
        channel = supabase.channel(`room-${activeRoomId}`);
        
        // Listen for room data changes
        channel.on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'game_rooms', 
        filter: `id=eq.${activeRoomId}` 
      }, (payload) => {
        const newData = payload.new as GameRoom;
        const newQuestionId = newData?.current_question_data?.questionId;
        const newSequence = newData?.question_sequence || 0;
        const currentSequence = currentRoomGameData?.question_sequence || 0;
        
        // Ignore updates with older sequence numbers
        if (newSequence < currentSequence) {
          console.log(`[Realtime] Ignoring stale update - sequence ${newSequence} < current ${currentSequence}`);
          return;
        }
        
        // Use previousQuestionIdRef which is properly maintained
        const isQuestionChange = previousQuestionIdRef.current && 
                                newQuestionId && 
                                previousQuestionIdRef.current !== newQuestionId;
        
        console.log(`[Realtime] Specific room update for ${activeRoomId}`, {
          hasTransitionUntil: !!newData?.transition_until,
          transitionUntil: newData?.transition_until,
          hasNextQuestionData: !!newData?.next_question_data,
          currentQuestionId: newQuestionId,
          previousQuestionId: previousQuestionIdRef.current,
          isQuestionChange,
          roundNumber: newData?.current_round_number,
          questionSequence: newSequence,
          answersCount: newData?.current_round_answers?.length,
          status: newData?.status
        });
        
        // CRITICAL: If transition_until is set and we have next question data, this means
        // all players have answered and we should prepare for transition
        if (newData?.transition_until && newData?.next_question_data) {
          console.log(`[Realtime] TRANSITION DETECTED - Server has set transition period`);
          console.log(`[Realtime] Transition will occur at: ${newData.transition_until}`);
        }
        
        // If this is a question change, reset submission state for all players
        if (isQuestionChange) {
          console.log('[Realtime] Question change detected - resetting submission state', {
            fromQuestion: previousQuestionIdRef.current,
            toQuestion: newQuestionId,
            userId: user?.id,
            isHost: user?.id === newData?.host_id,
            currentAnswersLength: newData?.current_round_answers?.length || 0
          });
          
          // Reset all submission-related state
          setHasSubmittedCurrentRound(false);
          setSubmittedAnswerForQuestion(null);
          submissionInProgressRef.current = false;
          previousQuestionIdRef.current = newQuestionId;
          
          // Clear transition tracking to allow new transitions
          transitionedQuestionIdRef.current = null;
          transitionInProgressRef.current = false;
          
          // Clear any optimistic answers
          setOptimisticAnswer(null);
          
          // For non-hosts, this realtime update IS the transition
          if (user?.id !== newData?.host_id) {
            console.log('[Realtime] Non-host received question transition through realtime');
          }
        }
        
        setCurrentRoomGameData(() => newData);
      });

        // Listen for player changes in this room
        channel.on('postgres_changes', { 
          event: '*', 
          schema: 'public', 
          table: 'game_players', 
          filter: `room_id=eq.${activeRoomId}` 
        }, (payload) => {
          console.log(`[Realtime] *** PLAYER UPDATE EVENT *** for room ${activeRoomId}`, {
            eventType: payload.eventType,
            old: payload.old,
            new: payload.new,
            userId: user?.id,
            isHost: user?.id === currentRoomGameData?.host_id,
            timestamp: new Date().toISOString()
          });
          
          // Use ref to get the latest version of the function
          if (fetchPlayersInActiveRoomRef.current) {
            console.log(`[Realtime] Triggering player fetch due to realtime event`);
            fetchPlayersInActiveRoomRef.current(activeRoomId, 'realtime_player_update');
          } else {
            console.error(`[Realtime] fetchPlayersInActiveRoomRef.current is null!`);
          }
        });

        channel.subscribe((status) => {
          console.log(`[Realtime] Channel subscription status change:`, {
            status,
            channel: channel.topic,
            activeRoomId,
            userId: user?.id,
            timestamp: new Date().toISOString()
          });
          
          if (status === 'SUBSCRIBED') {
            console.log('[Realtime] ✅ Successfully subscribed to room channel:', activeRoomId);
            console.log('[Realtime] Subscriptions active for:', {
              roomUpdates: true,
              playerUpdates: true,
              channelTopic: channel.topic
            });
          } else if (status === 'CHANNEL_ERROR') {
            console.error('[Realtime] ❌ Error subscribing to room channel:', activeRoomId);
          } else if (status === 'TIMED_OUT') {
            console.error('[Realtime] ⏱️ Subscription timed out for room channel:', activeRoomId);
          } else if (status === 'CLOSED') {
            console.log('[Realtime] 🔒 Channel closed:', activeRoomId);
          }
        });
      }

    } else if (selectedOverallGameType === 'multiplayer') {
      // ---- STATE 2: IN THE LOBBY ----
      // Only cleanup room-specific channels, preserve lobby channel
      const existingChannels = supabase.getChannels();
      existingChannels.forEach(ch => {
        if (ch.topic.startsWith('room-')) {
          console.log('[Realtime] Removing room channel:', ch.topic);
          supabase.removeChannel(ch);
        }
      });

      // Check if we already have a lobby channel
      const existingLobbyChannel = existingChannels.find(ch => ch.topic === 'lobby-global');
      if (existingLobbyChannel) {
        console.log('[Realtime] Lobby channel already exists, reusing');
        channel = existingLobbyChannel;
        return; // Exit early - channel already set up
      } else {
        console.log('[Realtime] ✅ Setting up NEW GLOBAL subscriptions for user in lobby');
        
        channel = supabase.channel(`lobby-global`);
      
      // Listen for changes to any game room (for lobby updates)
      channel.on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'game_rooms' 
      }, () => {
        console.log('[Realtime] Global room change detected - triggering lobby refresh');
        // Use ref to get the latest version of the function
        if (fetchAndSetGameRoomsRef.current) {
          fetchAndSetGameRoomsRef.current();
        }
      });

        channel.subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log('[Realtime] Successfully subscribed to lobby channel');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('[Realtime] Error subscribing to lobby channel');
          }
        });
      }
    } else {
      // ---- STATE 3: SINGLE PLAYER OR OTHER MODE ----
      console.log('[Realtime] In single-player mode - cleaning up all channels');
      supabase.removeAllChannels();
      return;
    }

    // This cleanup runs ONLY when user, activeRoomId, or game type changes
    return () => {
      if (channel) {
        console.log('[Realtime] Cleaning up channel:', channel.topic);
        supabase.removeChannel(channel);
      }
    };
  }, [user?.id, activeRoomId, selectedOverallGameType]); // Only stable dependencies


  // Fetch players when entering a waiting room (one-time fetch)
  // Plus host-only polling as a workaround for RLS realtime issues
  useEffect(() => {
    if (!activeRoomId || !currentRoomGameData || currentRoomGameData.status !== 'waiting') {
      return;
    }

    // Fetch players once when entering waiting room
    console.log('[Initial Fetch] Fetching players for waiting room:', activeRoomId);
    fetchPlayersInActiveRoomRef.current?.(activeRoomId, 'room_entry');

    // TEMPORARY WORKAROUND: Host-only polling for player updates
    // This is needed because RLS policies might block realtime events for the host
    const isHost = user?.id === currentRoomGameData.host_id;
    if (isHost && playersInRoom.length < 2) {
      console.log('[Host Polling] Starting host-only polling for player updates');
      
      const intervalId = setInterval(() => {
        // Skip if game is starting
        if (isStartingGameRef.current) {
          return;
        }
        
        console.log('[Host Polling] Checking for new players...');
        fetchPlayersInActiveRoomRef.current?.(activeRoomId, 'host_poll');
      }, 2000); // Poll every 2 seconds only for host waiting for players
      
      return () => {
        console.log('[Host Polling] Stopping host polling');
        clearInterval(intervalId);
      };
    }
  }, [activeRoomId, currentRoomGameData?.status, currentRoomGameData?.host_id, user?.id, playersInRoom.length]);

  // FINAL EFFECT 3: Manages Tab Visibility
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        console.log('[DISCONNECT_DETECTION] Page became visible - reconnecting real-time');
        // Just ensure real-time is connected, don't sync any state
        supabase.realtime.connect();
      }
    };

    const visibilityWrapper = () => {
      handleVisibilityChange().catch(err =>
        console.warn(
          '[DISCONNECT_DETECTION] final visibility handler rejected:',
          err instanceof Error ? err.message : err
        )
      );
    };
    document.addEventListener('visibilitychange', visibilityWrapper);
    return () => document.removeEventListener('visibilitychange', visibilityWrapper);
  }, [activeRoomId, syncFullRoomState, currentRoomGameData, playersInRoom]); // Include comprehensive sync function and game state

  // Log transition state for debugging

  // Monitor for all players submitted to ensure we catch transition updates
  useEffect(() => {
    if (!currentRoomGameData || currentRoomGameData.status !== 'active' || !activeRoomId) {
      return;
    }
    
    const currentQuestionId = currentRoomGameData.current_question_data?.questionId;
    if (!currentQuestionId) return;
    
    const answers = Array.isArray(currentRoomGameData.current_round_answers) 
      ? currentRoomGameData.current_round_answers 
      : [];
    
    const answersForCurrentQuestion = answers.filter(a => a.questionId === currentQuestionId);
    const allPlayersSubmitted = answersForCurrentQuestion.length === playersInRoom.length && playersInRoom.length > 0;
    
    // If all players have submitted but we don't have transition_until yet, force a sync
    if (allPlayersSubmitted && !currentRoomGameData.transition_until && !currentRoomGameData.next_question_data) {
      console.log('[SUBMISSION_MONITOR] All players submitted but no transition set - forcing sync', {
        answersCount: answersForCurrentQuestion.length,
        playersCount: playersInRoom.length,
        hasTransition: !!currentRoomGameData.transition_until
      });
      
      // Debounce to avoid multiple syncs
      const timer = setTimeout(async () => {
        console.log('[SUBMISSION_MONITOR] Executing forced sync for transition detection');
        await syncFullRoomState(activeRoomId, 'all_players_submitted');
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [
    currentRoomGameData?.current_round_answers,
    currentRoomGameData?.transition_until,
    currentRoomGameData?.status,
    currentRoomGameData?.current_question_data?.questionId,
    playersInRoom.length,
    activeRoomId,
    syncFullRoomState
  ]);

  // REMOVED: Client-side transition monitoring - server now handles all transitions
  // The server transition-monitor function will advance games automatically
  /*
  useEffect(() => {
    // Clear any existing transition timer when dependencies change
    if (transitionTimerRef.current) {
      clearTimeout(transitionTimerRef.current);
      transitionTimerRef.current = null;
    }

    if (!currentRoomGameData?.transition_until || currentRoomGameData.status !== 'active') {
      return;
    }*/
    // ENTIRE TRANSITION_MONITOR EFFECT REMOVED - Server handles all transitions
  // }, [currentRoomGameData?.transition_until, currentRoomGameData?.next_question_data?.questionId, currentRoomGameData?.status, activeRoomId, supabase]);

  // EVENT-BASED TRANSITION HELPER - Client reminds server when transition is due
  useEffect(() => {
    // Only watch for transition deadlines in active multiplayer games
    if (!activeRoomId || !currentRoomGameData || currentRoomGameData.status !== 'active') {
      return;
    }

    const transitionDeadline = currentRoomGameData.transition_until;
    if (!transitionDeadline) {
      return;
    }

    // Calculate delay until transition should occur
    const deadline = new Date(transitionDeadline).getTime();
    const now = Date.now();
    const delay = Math.max(0, deadline - now + 100); // Add 100ms buffer

    // Skip if deadline already passed by more than 10 seconds (stale data)
    if (delay > 10000) {
      console.log(`[TRANSITION_HELPER] Skipping stale deadline (${delay}ms in future)`);
      return;
    }

    console.log(`[TRANSITION_HELPER] Setting timer for ${delay}ms to check transition for room ${activeRoomId}`);

    let retryCount = 0;
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second between retries

    const checkTransition = async () => {
      console.log(`[TRANSITION_HELPER] Transition deadline reached, asking server to check room ${activeRoomId} (attempt ${retryCount + 1})`);
      
      try {
        // Ask server to check if this game needs transitioning
        const { data, error } = await supabase.functions.invoke('check-game-transition', {
          body: { roomId: activeRoomId }
        });

        if (error) {
          console.error('[TRANSITION_HELPER] Error checking transition:', error);
          
          // Retry on error if we haven't exceeded max retries
          if (retryCount < maxRetries - 1) {
            retryCount++;
            console.log(`[TRANSITION_HELPER] Retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})`);
            setTimeout(checkTransition, retryDelay);
          } else {
            console.error('[TRANSITION_HELPER] Max retries reached, giving up');
          }
        } else {
          console.log('[TRANSITION_HELPER] Server response:', data);
          
          // If transition didn't happen but should have, retry
          if (data && !data.transitioned && retryCount < maxRetries - 1) {
            retryCount++;
            console.log(`[TRANSITION_HELPER] Transition not processed, retrying in ${retryDelay}ms`);
            setTimeout(checkTransition, retryDelay);
          }
        }
      } catch (err) {
        console.error('[TRANSITION_HELPER] Exception checking transition:', err);
        
        // Retry on exception if we haven't exceeded max retries
        if (retryCount < maxRetries - 1) {
          retryCount++;
          console.log(`[TRANSITION_HELPER] Retrying after exception in ${retryDelay}ms`);
          setTimeout(checkTransition, retryDelay);
        }
      }
    };

    const timer = setTimeout(checkTransition, delay);

    return () => {
      console.log('[TRANSITION_HELPER] Clearing transition timer');
      clearTimeout(timer);
    };
  }, [activeRoomId, currentRoomGameData?.transition_until, currentRoomGameData?.status, supabase]);

  // REMOVED: Old client-side question transition logic - replaced with event-based helper above
  /*
  useEffect(() => {
    if (!activeRoomId || !currentRoomGameData || currentRoomGameData.status !== 'active') {
      console.log('[QUESTION_TRANSITION] Effect skipped - missing data or inactive game', {
        activeRoomId,
        hasGameData: !!currentRoomGameData,
        status: currentRoomGameData?.status
      });
      return;
    }
    
    const currentQuestionId = currentRoomGameData.current_question_data?.questionId;
    const questionStartedAt = currentRoomGameData.question_started_at;
    const transitionUntil = currentRoomGameData.transition_until;
    const nextQuestionData = currentRoomGameData.next_question_data;
    
    // Clear transition ref if we have a new question
    if (currentQuestionId && transitionedQuestionIdRef.current && transitionedQuestionIdRef.current !== currentQuestionId) {
      const currentAnswers = currentRoomGameData.current_round_answers || [];
      const hasAnswersForNewQuestion = currentAnswers.some(a => a.questionId === currentQuestionId);
      const hasOnlyOldAnswers = currentAnswers.length > 0 && !hasAnswersForNewQuestion;
      
      if (currentAnswers.length === 0 || hasOnlyOldAnswers) {
        console.log('[QUESTION_TRANSITION] New question detected, clearing transition ref and submission state', {
          oldQuestion: transitionedQuestionIdRef.current,
          newQuestion: currentQuestionId,
          currentAnswersLength: currentAnswers.length,
          hasOnlyOldAnswers
        });
        transitionedQuestionIdRef.current = null;
        transitionInProgressRef.current = false;
        
        // Reset submission state for the new question
        setSubmittedAnswerForQuestion(null);
        setHasSubmittedCurrentRound(false);
        submissionInProgressRef.current = false;
        setHasAnswered(false);
        
        // CRITICAL FIX: Clear animation state when new question is detected
        setEnhancedAnimatedAnswers({});
        triggeredAnimationsRef.current.clear();
        // Also clear any active global animations
        setGlobalAnimations([]);
        
        console.log('[QUESTION_TRANSITION] Reset submission state and animation state for new question');
      }
    }
    
    // Check if we've already transitioned for this question
    const alreadyTransitioned = transitionedQuestionIdRef.current === currentQuestionId;
    const shouldTransition = !alreadyTransitioned && !transitionInProgressRef.current;
    
    // Calculate timings
    const now = Date.now();
    const timeSinceQuestionStart = questionStartedAt ? now - new Date(questionStartedAt).getTime() : 0;
    const remainingTimeForHardCap = questionStartedAt ? Math.max(0, 7000 - timeSinceQuestionStart) : 7000;
    const timeUntilTransition = transitionUntil ? Math.max(0, new Date(transitionUntil).getTime() - now) : null;
    
    // Log transition check details
    console.log('[QUESTION_TRANSITION] Checking transition conditions:', {
      currentQuestionId,
      shouldTransition,
      transitionUntil,
      hasNextQuestionData: !!nextQuestionData,
      timeUntilTransition: timeUntilTransition ? Math.round(timeUntilTransition / 1000) + 's' : 'none',
      timeSinceQuestionStart: Math.round(timeSinceQuestionStart / 1000) + 's',
      remainingTimeForHardCap: Math.round(remainingTimeForHardCap / 1000) + 's',
      alreadyTransitioned,
      transitionInProgress: transitionInProgressRef.current,
      transitionedQuestionId: transitionedQuestionIdRef.current
    });
    
    // Function to advance to next question
    const advanceToNextQuestion = async (reason: string) => {
      console.log(`[QUESTION_TRANSITION] Advancing to next question (${reason})`);
      
      // If we have pre-generated next question data from server, use it directly
      if (nextQuestionData && transitionUntil) {
        console.log('[QUESTION_TRANSITION] Using pre-generated next question data from server');
        
        transitionInProgressRef.current = true;
        transitionedQuestionIdRef.current = currentQuestionId || null;
        
        // Update local state with the next question
        const newRoundNumber = (currentRoomGameData.current_round_number || 1) + 1;
        
        setCurrentRoomGameData(prev => ({
          ...prev!,
          current_question_data: nextQuestionData,
          current_round_answers: [],
          current_round_number: newRoundNumber,
          question_started_at: new Date().toISOString(),
          transition_until: null,
          next_question_data: null,
          last_activity_timestamp: new Date().toISOString()
        }));
        
        // Reset submission state
        setSubmittedAnswerForQuestion(null);
        setHasSubmittedCurrentRound(false);
        submissionInProgressRef.current = false;
        setHasAnswered(false);
        
        // CRITICAL FIX: Clear animation state when advancing to next question
        setEnhancedAnimatedAnswers({});
        triggeredAnimationsRef.current.clear();
        // Also clear any active global animations to prevent blocking
        setGlobalAnimations([]);
        
        // CRITICAL FIX: Update the server to clear current_round_answers
        console.log('[QUESTION_TRANSITION] Updating server to clear answers and advance question');
        try {
          const { error: updateError } = await supabase
            .from('game_rooms')
            .update({
              current_question_data: nextQuestionData,
              current_round_answers: [], // Clear the answers on the server
              current_round_number: newRoundNumber,
              question_started_at: new Date().toISOString(),
              transition_until: null,
              next_question_data: null,
              last_activity_timestamp: new Date().toISOString()
            })
            .eq('id', activeRoomId)
            .eq('status', 'active');
            // REMOVED: .not('transition_until', 'is', null) condition
            // This was causing updates to fail if transition_until was already cleared
            
          if (updateError) {
            console.error('[QUESTION_TRANSITION] Failed to update server:', updateError);
            // CRITICAL FIX: Revert local state if server update fails
            transitionInProgressRef.current = false;
          } else {
            console.log('[QUESTION_TRANSITION] Server updated successfully - answers cleared');
            // Only clear transition flag after successful server update
            setTimeout(() => {
              transitionInProgressRef.current = false;
            }, 500); // Increased delay to ensure server state propagates
          }
        } catch (error) {
          console.error('[QUESTION_TRANSITION] Exception updating server:', error);
          // Clear transition flag on exception
          transitionInProgressRef.current = false;
        }
        
        // CRITICAL FIX: Do NOT sync immediately after optimistic update
        // This was causing the race condition where stale server data would overwrite
        // the optimistically updated local state
        // The next sync will happen naturally through the subscription or visibility changes
        console.log('[QUESTION_TRANSITION] Skipping immediate sync to prevent race condition');
        return;
      }
      
      // Fallback: Call the API if no pre-generated data (shouldn't happen normally)
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/next-question-handler`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
          },
          body: JSON.stringify({ 
            roomId: activeRoomId,
            forceTransition: reason === '7-second hard cap reached'
          }),
        });

        if (!response.ok) {
          let errorData;
          try {
            errorData = await response.json();
          } catch (jsonError) {
            console.error('[QUESTION_TRANSITION] Failed to parse error response:', jsonError);
            errorData = { error: 'Failed to parse response', status: response.status };
          }
          
          if (response.status === 409 && errorData.alreadyAdvanced) {
            console.log('[QUESTION_TRANSITION] Another player already advanced the question, which is expected behavior');
            transitionInProgressRef.current = false;
            return;
          }
          
          console.error('[QUESTION_TRANSITION] Failed to advance to next question:', {
            status: response.status,
            statusText: response.statusText,
            errorData,
            errorMessage: errorData?.error || errorData?.message || 'Unknown error',
            details: errorData?.details || 'No details available'
          });
          transitionInProgressRef.current = false;
          transitionedQuestionIdRef.current = null;
          return;
        }

        const result = await response.json();
        console.log('[QUESTION_TRANSITION] Successfully advanced to next question:', result);
        transitionInProgressRef.current = false;
        
        console.log('[QUESTION_TRANSITION] Transition complete - real-time will propagate changes');
      } catch (err) {
        console.error('[QUESTION_TRANSITION] Failed to advance to next question:', {
          error: err instanceof Error ? err.message : String(err),
          stack: err instanceof Error ? err.stack : undefined,
          fullError: err
        });
        transitionInProgressRef.current = false;
        transitionedQuestionIdRef.current = null;
      }
    };
    
    // Clear any existing timers before setting new ones
    if (hardCapTimerRef.current) {
      clearTimeout(hardCapTimerRef.current);
      hardCapTimerRef.current = null;
    }
    
    if (shouldTransition) {
      // Monitor server-controlled transition timing
      if (transitionUntil && timeUntilTransition !== null) {
        // If transition has already expired, advance immediately
        if (timeUntilTransition <= 0) {
          console.log('[QUESTION_TRANSITION] Transition already expired, advancing immediately');
          if (!transitionInProgressRef.current) {
            transitionInProgressRef.current = true;
            transitionedQuestionIdRef.current = currentQuestionId || null;
            advanceToNextQuestion('server transition period already expired');
          }
        }
        // Note: We don't set a timer here because TRANSITION_MONITOR handles this
      }
      
      // Set up 7-second hard cap timer from question start
      if (questionStartedAt && remainingTimeForHardCap > 0 && !transitionUntil) {
        console.log('[QUESTION_TRANSITION] Setting 7-second hard cap timer', {
          remainingTime: Math.round(remainingTimeForHardCap / 1000) + 's'
        });
        
        hardCapTimerRef.current = setTimeout(() => {
          if (!transitionInProgressRef.current) {
            transitionInProgressRef.current = true;
            transitionedQuestionIdRef.current = currentQuestionId || null;
            advanceToNextQuestion('7-second hard cap reached');
          }
        }, remainingTimeForHardCap);
      }
    }
    
    return () => {
      if (hardCapTimerRef.current) {
        clearTimeout(hardCapTimerRef.current);
        hardCapTimerRef.current = null;
      }
    };
  }, [
    activeRoomId, 
    currentRoomGameData,
    currentRoomGameData?.transition_until, // Add explicit dependency
    currentRoomGameData?.next_question_data, // Add explicit dependency
    currentRoomGameData?.current_question_data?.questionId, // Add explicit dependency
    playersInRoom.length, 
    supabase, 
    syncFullRoomState,
    user?.id
  ]);
  */

  // Update handleStartGame function after handleToggleReady
  const handleStartGame = async () => {
    // CRITICAL: Early exit with detailed logging if already starting
    if (isStartingGame) {
      console.warn("[Client] Start game request already in progress - blocking duplicate request.");
      console.log("[Client] [RACE_CONDITION_PREVENTION] isStartingGame flag prevented duplicate start game call", {
        timestamp: new Date().toISOString(),
        activeRoomId,
        userId: user?.id,
        currentFlag: isStartingGame
      });
      return;
    }

    console.log("[Client] [START_GAME_ATTEMPT] Beginning start game process", {
      timestamp: new Date().toISOString(),
      activeRoomId,
      userId: user?.id,
      userIsHost: user && gameRooms.find(room => room.id === activeRoomId)?.host_id === user.id,
      playersInRoomCount: playersInRoom.length,
      allPlayersReady: playersInRoom.length > 0 && playersInRoom.every(p => p.is_ready),
      currentRoomStatus: gameRooms.find(room => room.id === activeRoomId)?.status
    });

    // CRITICAL: Ensure you have the LATEST room details and current user
    if (!activeRoomId || !user || !user.id) {
      console.error("[Client] Start game conditions not met: Missing user or room information.", {
        activeRoomId: !!activeRoomId,
        user: !!user,
        userId: !!user?.id
      });
      setErrorMp("Cannot start game: Missing user or room information.");
      return;
    }

    // CRITICAL FIX: Fetch fresh room details from server instead of relying on potentially stale gameRooms array
    console.log("[Client] [START_GAME] Fetching latest room details from server before host check...");
    let currentRoomDetails;
    try {
      const { data: freshRoomData, error: roomFetchError } = await supabase
        .from('game_rooms')
        .select('*')
        .eq('id', activeRoomId)
        .single();

      if (roomFetchError || !freshRoomData) {
        console.error("[Client] Start game conditions not met: Failed to fetch current room details", roomFetchError);
        setErrorMp("Failed to fetch room details. Cannot start game.");
        return;
      }

      currentRoomDetails = freshRoomData;
      console.log("[Client] [START_GAME] Fresh room data fetched:", {
        roomId: activeRoomId,
        status: currentRoomDetails.status,
        hostId: currentRoomDetails.host_id
      });
    } catch (fetchError) {
      console.error("[Client] Exception fetching room details:", fetchError);
      setErrorMp("Exception fetching room details. Cannot start game.");
      return;
    }

    // CRITICAL: Double-check host status with latest data
    const isCurrentUserHost = currentRoomDetails.host_id === user.id;
    console.log("[Client] [START_GAME] Host verification check:", {
      currentUserId: user.id,
      roomHostId: currentRoomDetails.host_id,
      isCurrentUserHost,
      roomStatus: currentRoomDetails.status
    });

    if (!isCurrentUserHost) {
      console.error("[Client] Start game conditions not met: Not the host.", { 
        hostId: currentRoomDetails.host_id, 
        currentUserId: user.id,
        roomId: activeRoomId
      });
      setErrorMp("Only the host can start the game.");
      return;
    }

    // Validate game can be started
    const allPlayersReady = playersInRoom.length > 0 && playersInRoom.every(p => p.is_ready);
    const minPlayersMet = playersInRoom.length >= 2; // Keep requirement of 2 players
    const roomIsWaiting = currentRoomDetails.status === 'waiting';

    console.log("[Client] [START_GAME] Pre-flight validation:", {
      allPlayersReady,
      minPlayersMet,
      roomIsWaiting,
      playerCount: playersInRoom.length,
      roomStatus: currentRoomDetails.status
    });

    // CRITICAL FIX: Handle case where game has already started (state synchronization)
    if (!roomIsWaiting) {
      console.warn(`[Client] [STATE_SYNC_FIX] Attempted to start a game that is not in 'waiting' status (current: ${currentRoomDetails.status}). Forcing state synchronization.`);

      // The game is already active. The client is out of sync.
      // Force the client to catch up to the correct state.
      setCurrentRoomGameData(currentRoomDetails); // Update state with the correct room data

      // Also update the gameRooms array to keep it in sync
      setGameRooms(prevRooms =>
        prevRooms.map(room => room.id === activeRoomId ? { ...room, ...currentRoomDetails } : room)
      );

      // The UI will now re-render based on the correct status,
      // which should show the game view instead of the lobby.
      console.log(`[Client] [STATE_SYNC_FIX] State synchronization complete. UI should now reflect status: ${currentRoomDetails.status}`);

      setIsStartingGame(false); // Make sure to clear the loading state
      isStartingGameRef.current = false; // Clear ref as well
      return; // Stop the function here
    }

    if (!minPlayersMet) {
      console.warn("[Client] Minimum players not met.", {
        playerCount: playersInRoom.length,
        required: 2
      });
      setErrorMp("Cannot start game: minimum 2 players required.");
      return;
    }

    if (!allPlayersReady) {
      console.warn("[Client] Not all players are ready.", {
        totalPlayers: playersInRoom.length,
        readyPlayers: playersInRoom.filter(p => p.is_ready).length
      });
      setErrorMp("Cannot start game: all players must be ready.");
      return;
    }

    // CRITICAL: Set flags IMMEDIATELY before starting and clear any previous errors
    console.log(`[Client] Host ${user.id} attempting to start game in room ${activeRoomId} - setting isStartingGame to TRUE`);
    setIsStartingGame(true);
    isStartingGameRef.current = true; // Also set ref to pause periodic fetches
    setErrorMp(null); // Clear any previous errors

    try {
      console.log(`[Client] [START_GAME_EDGE_CALL] Invoking start-game-handler for room ${activeRoomId}`);
      const { data, error: invokeError } = await supabase.functions.invoke('start-game-handler', {
        body: { roomId: activeRoomId },
      });

      if (invokeError) {
        console.error("[Client] Error invoking start-game-handler:", invokeError);

        // Enhanced error handling for Edge Function responses
        let errorMessage = 'Failed to start game.';
        let shouldTransitionToGame = false;

        // Try to extract detailed error information from the server response
        try {
          if (invokeError.context && invokeError.context.json) {
            const errorData = invokeError.context.json;
            errorMessage = errorData.error || errorData.message || invokeError.message;

            // Handle specific conflict scenarios where game was actually started
            if (errorData.conflictType === 'RACE_CONDITION_ALREADY_STARTED' ||
                errorData.conflictType === 'CONCURRENT_START_ATTEMPT' ||
                (errorData.currentStatus === 'active' && errorData.gameStartTime)) {
              console.log("[Client] [START_GAME_409_HANDLING] Game was already started by concurrent request", {
                conflictType: errorData.conflictType,
                currentStatus: errorData.currentStatus,
                gameStartTime: errorData.gameStartTime,
                suggestion: errorData.suggestion
              });

              // Since the game was actually started (just not by this request), transition to active game
              shouldTransitionToGame = true;
              errorMessage = "Game started successfully (by concurrent request)";
            }

            // Handle specific validation errors with helpful messages
            if (errorData.error === 'Not all players are ready.') {
              errorMessage = `Cannot start game: ${errorData.details || 'Not all players are ready'}`;
              console.log("[Client] [START_GAME_VALIDATION] Player readiness validation failed:", {
                readyCount: errorData.readyCount,
                totalCount: errorData.totalCount,
                details: errorData.details
              });
            } else if (errorData.error === 'Minimum players not met.') {
              errorMessage = `Cannot start game: ${errorData.details || 'Need at least 2 players'}`;
              console.log("[Client] [START_GAME_VALIDATION] Minimum players validation failed:", {
                currentCount: errorData.currentCount,
                requiredCount: errorData.requiredCount,
                details: errorData.details
              });
            }
          } else if (invokeError.message) {
            errorMessage = invokeError.message;
          }
        } catch (parseError) {
          console.warn("[Client] Failed to parse error response, using fallback message:", parseError);
          errorMessage = invokeError.message || 'Failed to start game - server error';
        }

        if (shouldTransitionToGame) {
          console.log("[Client] [START_GAME_RECOVERY] Transitioning to active game despite 409 error");
          // Don't set error - the game is actually started
          // Let realtime handle the state update naturally - no forced refresh needed
        } else {
          console.error("[Client] [START_GAME_ERROR] Setting error message:", errorMessage);
          setErrorMp(errorMessage);
        }
      } else {
        console.log("[Client] [START_GAME_SUCCESS] Successfully invoked start-game-handler:", data);

        // --- CRITICAL FIX: Optimistic UI Update ---
        // Don't wait for the realtime event. The call was successful, so we know the game has started.
        // Update the local state immediately to make the UI feel instantaneous.
        console.log('[Client] [OPTIMISTIC_UPDATE] Forcing UI transition to active game state.');
        setCurrentRoomGameData(prevData => {
          if (!prevData) return null; // Should not happen here

          // Extract relevant info from Edge Function response
          const {
            firstQuestion,
            gameStartTime,
            roundEndsAt,
            roundNumber
          } = (data || {}) as {
            firstQuestion?: PlayerQuestion;
            gameStartTime?: string;
            roundEndsAt?: string;
            roundNumber?: number;
          };

          return {
            ...prevData,
            status: 'active',
            current_question_data: firstQuestion ?? prevData.current_question_data,
            current_round_number: roundNumber ?? prevData.current_round_number,
            current_round_answers: [],
            game_start_timestamp: gameStartTime ?? prevData.game_start_timestamp,
            current_round_ends_at: roundEndsAt ?? prevData.current_round_ends_at
          } as GameRoom;
        });
        // --- END OF OPTIMISTIC UPDATE FIX ---

        // No need to fetch game rooms - the optimistic update handles the UI transition
        // and realtime will sync any other necessary state
      }
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error("[Client] Client-side exception invoking start-game-handler:", error);
      setErrorMp(`Client-side exception: ${error.message}`);
    } finally {
      // CRITICAL: Always clear the flags in finally block
      console.log("[Client] [START_GAME_CLEANUP] Clearing isStartingGame flags");
      setIsStartingGame(false);
      isStartingGameRef.current = false; // Clear ref to resume periodic fetches
    }
  };

  // Helper function to calculate position-based scoring for multiplayer
  const calculatePositionBasedScore = (position: number): number => {
    switch (position) {
      case 1: return 10; // First correct answer
      case 2: return 5;  // Second correct answer
      case 3: return 3;  // Third correct answer
      default: return 1; // Fourth and beyond
    }
  };

  // Add handleMultiplayerAnswerSubmit function after handleStartGame
  const handleMultiplayerAnswerSubmit = async (choiceName: string) => {
    // Guards: Don't submit if no user, no room, or answer already submitted for this question
    if (!user || !activeRoomId || submittedAnswerForQuestion) {
      return;
    }

    // CRITICAL: Additional check to prevent double submission
    if (hasSubmittedCurrentRound || submissionInProgressRef.current) {
      console.log('[Client] Answer submission blocked - already submitted', {
        hasSubmittedCurrentRound,
        submittedAnswerForQuestion,
        submissionInProgress: submissionInProgressRef.current
      });
      return;
    }
    
    // Set ref immediately to prevent any race conditions
    submissionInProgressRef.current = true;

    // Immediately update UI state to prevent double-submission
    // Use flushSync to ensure immediate DOM update
    // IMPORTANT: Once set, hasSubmittedCurrentRound stays true until next question
    flushSync(() => {
      setSubmittedAnswerForQuestion(choiceName);
      setHasSubmittedCurrentRound(true);
      setIsSubmittingAnswer(true);
      console.log('[SUBMISSION_STATE] Set hasSubmittedCurrentRound = true for answer:', choiceName);
    });

    console.log(`[Client] User ${user.id} submitting answer "${choiceName}" for room ${activeRoomId}`);

    // --- OPTIMISTIC UI UPDATE ---
    // WHY: We immediately add the current user to the list of submitted players
    // in the LOCAL state. This makes the UI feel instant for the person answering.
    const questionData = currentRoomGameData?.current_question_data;
    const selectedChoice = questionData?.choices.find(choice => choice.name === choiceName);

    if (selectedChoice && questionData) {
      const optimisticAnswerData = {
        userId: user.id,
        questionId: questionData.questionId,
        choiceName: choiceName,
        timestamp: Date.now(),
        isCorrect: selectedChoice.isCorrect,
        isPending: true
      };

      console.log('[OPTIMISTIC_ANSWER_DEBUG] Setting optimistic answer:', {
        choiceName,
        selectedChoiceIsCorrect: selectedChoice.isCorrect,
        optimisticAnswerData,
        questionChoices: questionData.choices.map(c => ({ name: c.name, isCorrect: c.isCorrect }))
      });

      // Use flushSync to force immediate synchronous rendering
      flushSync(() => {
        setOptimisticAnswer(optimisticAnswerData);
        setHasSubmittedCurrentRound(true); // Prevent double submission
        
        // CRITICAL: Set enhanced animation data for optimistic answer
        if (selectedChoice.isCorrect) {
          const animationTrigger = Date.now();
          const playerBonusLevels = currentRoomGameData?.player_bonus_levels || {};
          const bonusLevel = playerBonusLevels[user.id] || 0;
          
          // Calculate position-based score
          // Count existing correct answers to determine position
          const existingCorrectAnswers = (currentRoomGameData.current_round_answers || [])
            .filter(a => a.isCorrect && a.questionId === questionData.questionId);
          const position = existingCorrectAnswers.length + 1; // +1 because this is the new answer
          const scoreIncrease = calculatePositionBasedScore(position);
          
          console.log('[POSITION_BASED_SCORING] Calculating score for position:', {
            position,
            existingCorrectAnswers: existingCorrectAnswers.length,
            scoreIncrease
          });
          
          console.log('[OPTIMISTIC_FOOTBALL_DEBUG] Setting enhanced animation for optimistic answer:', {
            userId: user.id.substring(0, 8),
            bonusLevel,
            scoreIncrease,
            trigger: animationTrigger
          });
          
          setEnhancedAnimatedAnswers(prev => ({
            ...prev,
            [user.id]: {
              trigger: animationTrigger,
              bonusLevel: bonusLevel,
              scoreIncrease: scoreIncrease,
              questionId: questionData.questionId
            }
          }));
          
          // Trigger single-player animation for current user
          useGameStore.setState((state) => ({
            lastScoreChange: scoreIncrease,
            animationTrigger: state.animationTrigger + 1,
            streak: bonusLevel,
          }));
          
          // Clean up animation after duration
          setTimeout(() => {
            setEnhancedAnimatedAnswers(prev => {
              const newState = { ...prev };
              delete newState[user.id];
              return newState;
            });
          }, 5000);
        }
      });
    }
    // --- END OF OPTIMISTIC UPDATE ---

    try {
      console.log('[SUBMISSION_DEBUG] Starting answer submission to server:', {
        roomId: activeRoomId,
        choiceName,
        questionId: questionData?.questionId,
        userId: user.id.substring(0, 8) + '...',
        timestamp: new Date().toISOString()
      });

      const response = await supabase.functions.invoke('submit-answer-handler', {
        body: { 
          roomId: activeRoomId, 
          choiceName: choiceName,
          questionId: questionData?.questionId // Include question ID for validation
        },
      });

      console.log('[SUBMISSION_DEBUG] Server response received:', {
        hasError: !!response.error,
        errorMessage: response.error?.message,
        hasData: !!response.data,
        data: response.data
      });

      // Check if we got a non-2xx response
      if (response.error) {
        // Check the error message for 409 conflict indicators
        const errorMessage = response.error.message || '';
        if (errorMessage.includes('non-2xx') || errorMessage.includes('409')) {
          // Check if it's a question mismatch error
          if (errorMessage.includes('Question mismatch')) {
            console.error('[Client] Question mismatch detected - client is out of sync with server');
            // Force immediate room state sync to get correct question
            await syncFullRoomState(activeRoomId, 'question-mismatch-recovery');
            // Clear submission state to prevent confusion
            setHasSubmittedCurrentRound(false);
            setSubmittedAnswerForQuestion(null);
            submissionInProgressRef.current = false;
            alert('Your game was out of sync. Please try again.');
            return;
          }
          // This is expected - user already submitted
          console.log('[Client] Answer already submitted (409) - keeping submission state');
          return;
        }
        // Other errors should be thrown
        throw new Error(response.error.message);
      }
      
      const { data } = response;
      
      console.log('[Client] Answer submitted successfully:', data);
      
      // CRITICAL FIX: If server advanced the game, manually sync room state
      // This ensures we get the new question immediately, even if realtime is delayed
      if (data?.gameAdvanced) {
        console.log('[Client] Server advanced game to next question - manually syncing room state');
        await syncFullRoomState(activeRoomId, 'answer-submission-game-advanced');
      } else {
        // Trust real-time subscription to update UI after answer submission
        console.log('[Client] Answer submitted - real-time will handle UI updates');
      }

    } catch (err) {
      // CRITICAL: Clear optimistic answer on error so the UI doesn't show a false submission
      console.error('[SUBMISSION_ERROR] Failed to submit answer:', err);
      console.log('[SUBMISSION_ERROR] Clearing optimistic answer due to submission failure');
      
      // Clear the optimistic answer since it failed to submit
      setOptimisticAnswer(null);
      
      // Reset submission state to allow retry
      setHasSubmittedCurrentRound(false);
      setSubmittedAnswerForQuestion(null);
      submissionInProgressRef.current = false;
      
      // Show error to user
      alert('Failed to submit answer. Please try again.');
      
      console.log('[SUBMISSION_ERROR] Reset state to allow retry:', {
        hasSubmittedCurrentRound: false,
        submittedAnswerForQuestion: null,
        optimisticAnswer: null
      });
    } finally {
      setIsSubmittingAnswer(false);
      submissionInProgressRef.current = false;
    }
  };

  // Add effect to reset submission state when question changes and check existing answers
  useEffect(() => {
    if (currentRoomGameData?.current_question_data?.questionId && user) {
      const currentQuestionId = currentRoomGameData.current_question_data.questionId;
      
      // Check if this is actually a NEW question
      const isNewQuestion = previousQuestionIdRef.current !== currentQuestionId;
      
      if (isNewQuestion) {
        console.log('[SUBMISSION_STATE] NEW QUESTION DETECTED:', {
          previous: previousQuestionIdRef.current,
          current: currentQuestionId
        });
        previousQuestionIdRef.current = currentQuestionId;
        
        // Reset for new question - this is ALWAYS what we want for a new question
        setSubmittedAnswerForQuestion(null);
        setHasSubmittedCurrentRound(false);
        submissionInProgressRef.current = false;
        setHasAnswered(false);
        
        // CRITICAL FIX: Also clear animation refs when new question detected
        triggeredAnimationsRef.current.clear();
        
        console.log('[SUBMISSION_STATE] Reset submission state and animation refs for new question');
      }

      // Check if user has already submitted an answer for THIS SPECIFIC question
      const currentAnswers: GameAnswer[] = Array.isArray(currentRoomGameData.current_round_answers)
        ? currentRoomGameData.current_round_answers
        : [];

      // Debug logging to track answer state
      if (currentAnswers.length > 0) {
        console.log('[SUBMISSION_STATE_DEBUG] Answers in database:', {
          totalAnswers: currentAnswers.length,
          currentQuestionId,
          answerSummary: currentAnswers.map(a => ({
            userId: a.userId.substring(0, 8),
            questionId: a.questionId.substring(0, 8),
            matchesCurrentQ: a.questionId === currentQuestionId
          }))
        });
      }

      // CRITICAL FIX: Only consider answers that match the current question ID
      const userAnswer = currentAnswers.find(answer =>
        answer.userId === user.id && answer.questionId === currentQuestionId
      );

      if (userAnswer && userAnswer.questionId === currentQuestionId) {
        // User has already submitted for this SPECIFIC question
        console.log('[SUBMISSION_STATE] Found existing answer in database for current question:', userAnswer.choiceName);
        setHasSubmittedCurrentRound(true);
        setSubmittedAnswerForQuestion(userAnswer.choiceName);

        // Clear optimistic answer if real-time answer arrived
        if (optimisticAnswer && optimisticAnswer.userId === user.id && optimisticAnswer.questionId === currentQuestionId) {
          console.log('[Client] Real-time answer confirmed, clearing optimistic answer');
          setOptimisticAnswer(null);
        }
      } else {
        // No answer found in server data yet
        // IMPORTANT: Don't clear submission state here - it causes race conditions
        // The client should trust its own submission state until explicitly moving to a new question
        // Server data might be temporarily out of sync due to network delays
        
        // Only log if we have a submitted state but no server confirmation yet
        if (hasSubmittedCurrentRound || submittedAnswerForQuestion) {
          console.log('[SUBMISSION_STATE] Waiting for server confirmation - keeping local submission state', {
            hasSubmittedCurrentRound,
            submittedAnswerForQuestion,
            currentQuestionId,
            optimisticAnswer: optimisticAnswer?.questionId === currentQuestionId
          });
        }
      }
      // Removed the problematic else-if that was preserving state incorrectly
    }
  }, [currentRoomGameData?.current_question_data?.questionId, currentRoomGameData?.current_round_answers, user, user?.id, optimisticAnswer]);



  // Stabilized position object to prevent infinite loops in GlobalMultiplayerScoreAnimation
  const stableAnimationPosition = useMemo(() => {
    // Only create this when window is available to avoid SSR issues
    if (typeof window === 'undefined') return { x: 0, y: 0 };
    return { x: window.innerWidth * 0.8, y: window.innerHeight * 0.5 };
  }, []); // Empty dependency array - position is fixed

  // Cleanup effect for when leaving multiplayer or changing game status
  useEffect(() => {
    if (selectedOverallGameType !== 'multiplayer' || !activeRoomId || currentRoomGameData?.status === 'finished') {
      // Clear all animation state when leaving multiplayer or game finishes
      if (Object.keys(enhancedAnimatedAnswers).length > 0 || triggeredAnimationsRef.current.size > 0) {
        console.log('[ANIMATION_CLEANUP] Clearing animation state - leaving multiplayer or game finished');
        setEnhancedAnimatedAnswers({});
        triggeredAnimationsRef.current.clear();
      }
    }
  }, [selectedOverallGameType, activeRoomId, currentRoomGameData?.status]);

  // CONSOLIDATED Effect to handle both animation tracking AND triggering for multiplayer answers
  useEffect(() => {
    if (
      selectedOverallGameType !== 'multiplayer' ||
      !currentRoomGameData ||
      currentRoomGameData.status !== 'active' ||
      !currentRoomGameData.current_round_answers ||
      !currentRoomGameData.current_question_data?.questionId
    ) {
      console.log('[FOOTBALL_DEBUG] Animation trigger skipped - conditions not met:', {
        gameType: selectedOverallGameType,
        hasRoomData: !!currentRoomGameData,
        status: currentRoomGameData?.status,
        hasAnswers: !!currentRoomGameData?.current_round_answers,
        hasQuestionId: !!currentRoomGameData?.current_question_data?.questionId
      });
      return;
    }

    const currentQuestionId = currentRoomGameData.current_question_data.questionId;
    console.log('[FOOTBALL_DEBUG] Processing animations for question:', currentQuestionId, {
      answersCount: currentRoomGameData.current_round_answers.length,
      triggeredAnimationsCount: triggeredAnimationsRef.current.size
    });

    // Clear triggered animations when question changes
    if (triggeredAnimationsRef.current.size > 0) {
      const shouldClear = [...triggeredAnimationsRef.current].some((id: string) =>
        !id.includes(currentQuestionId)
      );
      if (shouldClear) {
        console.log('[FOOTBALL_DEBUG] Clearing triggered animations for new question');
        triggeredAnimationsRef.current.clear();
        // Also clear enhancedAnimatedAnswers when question changes
        setEnhancedAnimatedAnswers(() => ({}));
      }
    }

    // Process answers for IMMEDIATE animation triggering (no waiting for state updates)
    currentRoomGameData.current_round_answers.forEach((answer) => {
      if (!answer.isCorrect || answer.questionId !== currentQuestionId) {
        return;
      }

      // Create a stable unique ID based on the answer data (no Date.now() to prevent infinite loops)
      const uniqueAnimationId = `${answer.userId}-${answer.questionId}-${answer.timestamp}`;

      // Skip if already triggered
      if (triggeredAnimationsRef.current.has(uniqueAnimationId)) {
        return;
      }

      console.log('[FOOTBALL_DEBUG] Processing NEW correct answer for animation:', {
        userId: answer.userId.substring(0, 8) + '...',
        isCorrect: answer.isCorrect,
        questionId: answer.questionId,
        uniqueAnimationId
      });

      // Mark as triggered IMMEDIATELY
      triggeredAnimationsRef.current.add(uniqueAnimationId);

      // Get bonus levels for animation (football count)
      const playerBonusLevels = currentRoomGameData.player_bonus_levels || {};
      const bonusLevel = playerBonusLevels[answer.userId] || 0;
      
      // Calculate position-based score
      // Get all correct answers for this question and sort by timestamp
      const correctAnswersForQuestion = (currentRoomGameData.current_round_answers || [])
        .filter(a => a.isCorrect && a.questionId === currentQuestionId)
        .sort((a, b) => a.timestamp - b.timestamp);
      
      // Find position of this answer
      const position = correctAnswersForQuestion.findIndex(a => a.userId === answer.userId) + 1;
      const scoreIncrease = calculatePositionBasedScore(position);
      
      console.log('[POSITION_BASED_SCORING] Animation effect score calculation:', {
        userId: answer.userId.substring(0, 8),
        position,
        totalCorrectAnswers: correctAnswersForQuestion.length,
        scoreIncrease
      });

      // Trigger single-player animation for current user
      if (answer.userId === user?.id) {
        useGameStore.setState((state) => ({
          lastScoreChange: scoreIncrease,
          animationTrigger: state.animationTrigger + 1,
          streak: bonusLevel, // Use bonus level as streak for football count
        }));
      }

      console.log('[FOOTBALL_DEBUG] Triggering ENHANCED animation:', uniqueAnimationId, {
        scoreIncrease,
        bonusLevel,
        position: stableAnimationPosition,
        userId: answer.userId.substring(0, 8) + '...'
      });

      // Create unique trigger time for this animation
      const animationTrigger = Date.now();

      // CRITICAL FIX: Populate enhancedAnimatedAnswers for round submissions panel
      console.log('[FOOTBALL_ANIMATION_DEBUG] Setting enhancedAnimatedAnswers for user:', {
        userId: answer.userId.substring(0, 8),
        isCurrentUser: answer.userId === user?.id,
        bonusLevel,
        scoreIncrease,
        trigger: animationTrigger
      });
      
      setEnhancedAnimatedAnswers(prev => {
        const newState = {
          ...prev,
          [answer.userId]: {
            trigger: animationTrigger,
            bonusLevel: bonusLevel,
            scoreIncrease: scoreIncrease,
            questionId: answer.questionId
          }
        };
        console.log('[FOOTBALL_ANIMATION_DEBUG] enhancedAnimatedAnswers state after update:', newState);
        return newState;
      });

      const newAnimation = {
        id: uniqueAnimationId, // Use the unique ID here
        type: 'enhanced' as const,
        trigger: animationTrigger, // Use same trigger value
        scoreIncrease,
        bonusLevel,
        originPosition: stableAnimationPosition // Use the stable position object
      };

      // REMOVED: No longer play animation immediately - wait for card to land
      // Animation will be triggered by the landedAnswers effect instead
      console.log('[ANIMATION] Animation data prepared, waiting for card to land:', uniqueAnimationId);
    });
  }, [
    selectedOverallGameType,
    currentRoomGameData?.current_round_answers,
    currentRoomGameData?.current_question_data?.questionId,
    currentRoomGameData?.status,
    currentRoomGameData?.player_bonus_levels,
    user?.id,
    currentRoomGameData,
    stableAnimationPosition
    // Removed getElementPosition and stableAnimationPosition to prevent infinite loops
  ]);

  // DISABLED: Old effect - animation is now triggered directly in onAnimationEnd
  /*
  useEffect(() => {
    if (!landedAnswers.size || !currentRoomGameData?.current_question_data?.questionId) {
      return;
    }

    const currentQuestionId = currentRoomGameData.current_question_data.questionId;
    
    // Process each landed answer
    landedAnswers.forEach((answerKey) => {
      const [userId, questionId] = answerKey.split('-');
      
      // Skip if not for current question
      if (questionId !== currentQuestionId) {
        return;
      }
      
      // Find the answer data
      const answer = allAnswers.find(a => 
        a.userId === userId && a.questionId === questionId
      );
      
      if (!answer || !answer.isCorrect) {
        return;
      }
      
      // Check if this is the first correct answer for this question
      const existingFirstAnswerer = firstCorrectAnswererByQuestion.get(questionId);
      if (!existingFirstAnswerer) {
        // This is the first correct answer!
        setFirstCorrectAnswererByQuestion(prev => new Map(prev).set(questionId, userId));
        
        // Check if we have enhanced animation data for this user
        const enhancedData = enhancedAnimatedAnswers[userId];
        console.log('[FOOTBALL_ANIMATION_FLOW] Checking for enhanced data:', {
          userId: userId.substring(0, 8),
          questionId,
          hasEnhancedData: !!enhancedData,
          enhancedDataQuestionId: enhancedData?.questionId,
          currentQuestionId,
          dataMatches: enhancedData?.questionId === questionId,
          allEnhancedDataKeys: Object.keys(enhancedAnimatedAnswers)
        });
        
        if (enhancedData && enhancedData.questionId === questionId) {
          console.log('[FOOTBALL_ANIMATION_TIMING] First correct answer landed! Triggering football animation:', {
            userId: userId.substring(0, 8),
            questionId,
            enhancedData
          });
          
          // Wait a frame to ensure DOM is updated before getting position
          requestAnimationFrame(() => {
            // Get the actual position of the landed card
            const cardElement = document.querySelector(`[data-answer-key="${answerKey}"]`);
            let animationPosition = stableAnimationPosition;
            
            if (cardElement) {
              const rect = cardElement.getBoundingClientRect();
              // Use viewport coordinates for the animation
              animationPosition = {
                x: rect.left + rect.width / 2,
                y: rect.top + rect.height / 2
              };
              console.log('[FOOTBALL_ANIMATION_TIMING] Using card position:', {
                answerKey,
                rect: { left: rect.left, top: rect.top, width: rect.width, height: rect.height },
                animationPosition
              });
            } else {
              console.log('[FOOTBALL_ANIMATION_TIMING] Card element not found, using fallback position');
            }
            
            // Create and trigger the animation
            const uniqueAnimationId = `${userId}-${questionId}-${Date.now()}`;
            const newAnimation = {
              id: uniqueAnimationId,
              type: 'enhanced' as const,
              trigger: enhancedData.trigger,
              scoreIncrease: enhancedData.scoreIncrease,
              bonusLevel: enhancedData.bonusLevel,
              originPosition: animationPosition
            };
            
            console.log('[FOOTBALL_ANIMATION_TIMING] Created animation object:', {
              animation: newAnimation,
              enhancedData,
              animationPosition
            });
            
            console.log('[FOOTBALL_ANIMATION_TIMING] Scheduling football animation with delay for visual settling');
            
            // Wait for the slide-up animation to complete (0.75s) plus a small buffer
            // This ensures the card is at its final position before we calculate coordinates
            const SETTLE_DELAY = 850; // milliseconds (slide-up animation is 0.75s)
            
            setTimeout(() => {
              // Get the element position after the card has settled
              const cardElement = document.querySelector(`[data-answer-key="${answerKey}"]`);
              let finalAnimationPosition = stableAnimationPosition;
              
              if (cardElement) {
                const rect = cardElement.getBoundingClientRect();
                // Calculate position with -150px Y offset to match singleplayer behavior
                finalAnimationPosition = {
                  x: rect.left + rect.width / 2,
                  y: rect.top + rect.height / 2 - 150 // Add vertical offset like singleplayer
                };
                console.log('[FOOTBALL_ANIMATION_TIMING] Got final card position after settle:', {
                  answerKey,
                  rect: { left: rect.left, top: rect.top, width: rect.width, height: rect.height },
                  finalPosition: finalAnimationPosition
                });
              } else {
                console.log('[FOOTBALL_ANIMATION_TIMING] Card element not found after settle, using fallback position');
              }
              
              // Update the animation with the final position
              const finalAnimation = {
                ...newAnimation,
                originPosition: finalAnimationPosition
              };
              
              console.log('[FOOTBALL_ANIMATION_TIMING] Triggering football animation after settle delay!', {
                animationId: uniqueAnimationId,
                finalAnimation
              });
              setGlobalAnimations(prev => {
                const newAnimations = [...prev, finalAnimation];
                console.log('[FOOTBALL_ANIMATION_TIMING] Updated globalAnimations state:', {
                  previousCount: prev.length,
                  newCount: newAnimations.length,
                  newAnimation: finalAnimation
                });
                return newAnimations;
              });
            }, SETTLE_DELAY);
            
            // Clean up animation after duration (accounting for settle delay)
            setTimeout(() => {
              setGlobalAnimations(prev => prev.filter(anim => anim.id !== uniqueAnimationId));
              // Also clean up enhancedAnimatedAnswers
              setEnhancedAnimatedAnswers(prev => {
                const newState = { ...prev };
                delete newState[userId];
                return newState;
              });
            }, SETTLE_DELAY + 2500); // Reduced to ensure completion before transition
          });
        }
      } else {
        console.log('[FOOTBALL_ANIMATION_TIMING] Not first correct answer, skipping football animation:', {
          userId: userId.substring(0, 8),
          firstAnswerer: existingFirstAnswerer.substring(0, 8)
        });
      }
    });
  }, [landedAnswers, allAnswers, currentRoomGameData?.current_question_data?.questionId, 
      enhancedAnimatedAnswers, firstCorrectAnswererByQuestion, stableAnimationPosition]);
  */

  // Process pending animations when tab becomes visible

  // DISABLED: Enhanced animation data is now handled directly in onAnimationEnd
  /*
  useEffect(() => {
    if (!currentRoomGameData?.current_round_answers || !currentRoomGameData?.current_question_data) {
      return;
    }

    const currentQuestionId = currentRoomGameData.current_question_data.questionId;
    const answers = currentRoomGameData.current_round_answers as GameAnswer[];
    
    // Find all correct answers for current question
    const correctAnswers = answers.filter(a => 
      a.questionId === currentQuestionId && a.isCorrect
    );

    console.log('[ANIMATION_DATA_SYNC] Checking for new correct answers:', {
      currentQuestionId,
      correctAnswersCount: correctAnswers.length,
      existingAnimationData: Object.keys(enhancedAnimatedAnswers)
    });

    // Add animation data for any correct answer that doesn't have it yet
    correctAnswers.forEach(answer => {
      // Use functional update to avoid dependency on enhancedAnimatedAnswers
      setEnhancedAnimatedAnswers(prev => {
        if (!prev[answer.userId]) {
          console.log('[ANIMATION_DATA_SYNC] Adding animation data for player:', answer.userId.substring(0, 8));
          
          // Calculate bonus level based on position (1st = 3, 2nd = 2, 3rd = 1, rest = 0)
          const position = correctAnswers.findIndex(a => a.userId === answer.userId) + 1;
          const bonusLevel = position === 1 ? 3 : position === 2 ? 2 : position === 3 ? 1 : 0;
          const scoreIncrease = position === 1 ? 25 : position === 2 ? 15 : 10;
          
          // Clean up after animation duration
          // Need to ensure data persists for: slide-up (750ms) + settle (850ms) + animation (5000ms) = ~6.6s
          setTimeout(() => {
            setEnhancedAnimatedAnswers(prev => {
              const newState = { ...prev };
              delete newState[answer.userId];
              console.log('[ANIMATION_DATA_SYNC] Cleaning up animation data for user:', answer.userId.substring(0, 8));
              return newState;
            });
          }, 8000); // Increased from 5000ms to ensure data persists through entire animation flow
          
          return {
            ...prev,
            [answer.userId]: {
              trigger: Date.now(), // Unique trigger for each animation
              bonusLevel,
              scoreIncrease,
              questionId: currentQuestionId
            }
          };
        }
        return prev;
      });
    });
  }, [currentRoomGameData?.current_round_answers, currentRoomGameData?.current_question_data?.questionId]);
  */

  // Add new useEffect for debugging player scores panel
  useEffect(() => {
    if (currentRoomGameData?.status === 'active' && multiplayerPanelState === 'in_room') {
      console.log("[PlayerScoresPanel] Rendering with playersInRoom:", playersInRoom);
    }
  }, [playersInRoom, currentRoomGameData?.status, multiplayerPanelState, currentRoomGameData]);

  // Add this to window object for easy debugging in browser console
  if (typeof window !== 'undefined') {
    // ENHANCED: Add the new sign-out handler to window for debugging
    (window as { debugEnhancedSignOut?: () => Promise<void> }).debugEnhancedSignOut = handleSignOut;
    (window as { debugCurrentStates?: () => object }).debugCurrentStates = () => {
      return {
        activeRoomId,
        selectedRoomForDetail,
        playersInRoom: playersInRoom.length,
        multiplayerPanelState,
        centerPanelMpState,
        gameRooms: gameRooms.length,
        user: user?.id,
        timestamp: new Date().toISOString()
      };
    };
    
    // DEBUG: Enhanced room/player debugging functions
    (window as { debugCheckRoomCleanup?: (roomId?: string) => Promise<unknown> }).debugCheckRoomCleanup = async (roomId?: string) => {
      const targetRoomId = roomId || activeRoomId;
      if (!targetRoomId) {
        console.log('[DEBUG] No room ID provided and no active room');
        return;
      }
      
      console.log(`[DEBUG] Checking cleanup status for room: ${targetRoomId}`);
      
      // Check if room still exists
      const { data: roomData, error: roomError } = await supabase
        .from('game_rooms')
        .select('*')
        .eq('id', targetRoomId)
        .maybeSingle();
        
      console.log('[DEBUG] Room existence check:', { roomData, roomError });
      
      // Check game_players for this room
      const { data: playersData, error: playersError } = await supabase
        .from('game_players')
        .select('*')
        .eq('room_id', targetRoomId);
        
      console.log('[DEBUG] Players in room check:', { playersData, playersError });
      
      return {
        roomExists: !!roomData,
        roomData,
        playersInDb: playersData || [],
        summary: {
          roomDeleted: !roomData,
          playerCount: playersData?.length || 0,
          connectedPlayers: playersData?.filter(p => p.is_connected).length || 0,
          userStillInRoom: playersData?.some(p => p.user_id === user?.id) || false
        }
      };
    };
    
    // DEBUG: Test leave-room-handler directly
    (window as { debugTestLeaveRoomHandler?: (roomId?: string) => Promise<unknown> }).debugTestLeaveRoomHandler = async (roomId?: string) => {
      const targetRoomId = roomId || activeRoomId;
      if (!targetRoomId) {
        console.log('[DEBUG] No room ID provided and no active room');
        return;
      }
      
      console.log(`[DEBUG] Testing leave-room-handler for room: ${targetRoomId}`);
      
      try {
        const { data, error } = await supabase.functions.invoke('leave-room-handler', {
          body: { roomId: targetRoomId },
        });
        
        console.log('[DEBUG] leave-room-handler response:', { data, error });
        return { data, error };
      } catch (e) {
        console.error('[DEBUG] leave-room-handler exception:', e);
        return { exception: e };
      }
    };
    
    // DEBUG: Comprehensive state inspector
    (window as { debugInspectAllStates?: () => object }).debugInspectAllStates = () => {
      const states = {
        authentication: {
          user: user?.id,
          userProfile: userProfile?.username,
          isAuthLoading,
          isLoadingProfile
        },
        multiplayer: {
          selectedOverallGameType,
          multiplayerPanelState,
          centerPanelMpState,
          activeRoomId,
          selectedRoomForDetail: selectedRoomForDetail?.id,
          currentRoomGameData: currentRoomGameData?.id,
          playersInRoom: playersInRoom.length,
          gameRooms: gameRooms.length
        },
        flags: {
          isLoadingRooms,
          isCreatingRoom,
          isStartingGame,
          isSubmittingReady,
          isLeavingRoom,
          isJoiningOrRejoiningRoom,
          isCreatingAndJoiningRoom
        },
        errors: {
          errorMp,
          lobbyFetchError
        },
        timestamp: new Date().toISOString()
      };
      
      console.log('[DEBUG] Complete state inspection:', states);
      return states;
    };

    // **NEW**: Debug function to manually trigger fetchAndSetGameRooms
    (window as { debugFetchRooms?: () => Promise<unknown> }).debugFetchRooms = async () => {
      console.log('[DEBUG] Manually triggering fetchAndSetGameRooms...');
      try {
        await fetchAndSetGameRooms();
        console.log('[DEBUG] fetchAndSetGameRooms completed successfully');
        return { success: true, gameRoomsCount: gameRooms.length };
      } catch (error) {
        console.error('[DEBUG] fetchAndSetGameRooms failed:', error);
        return { success: false, error };
      }
    };

    // **NEW**: Debug function to inspect current query being used
    (window as { debugQueryInfo?: () => object }).debugQueryInfo = () => {
      return {
        userContext: user?.id,
        userAuthenticated: !!user,
        currentFilters: {
          table: 'game_rooms',
          statusIn: ['waiting', 'active'],
          aggregationMethod: 'game_players(count)',
          orderBy: 'created_at desc'
        },
        expectedBehavior: 'Should return ALL waiting/active rooms that RLS permits with correct player counts',
        troubleshootingHint: 'If returning 0 counts, check game_players RLS policies for count aggregation'
      };
    };

    // **NEW**: Debug function to test connection recovery
    (window as { debugConnectionRecovery?: () => object }).debugConnectionRecovery = () => {
      return {
        currentConnectionStatus: connectionStatus,
        activeRoomId,
        multiplayerPanelState,
        playersInRoom: playersInRoom.length,
        isOnline,
        actions: {
          simulateReconnecting: () => {
            console.log('[DEBUG] Simulating RECONNECTING state...');
            setConnectionStatus('RECONNECTING');
          },
          simulateConnected: () => {
            console.log('[DEBUG] Simulating CONNECTED state...');
            setConnectionStatus('CONNECTED');
          },
          simulateOffline: () => {
            console.log('[DEBUG] Simulating OFFLINE state...');
            setConnectionStatus('OFFLINE');
          },
          triggerRecovery: () => {
            console.log('[DEBUG] Triggering manual recovery attempt...');
            if (activeRoomId) {
              fetchPlayersInActiveRoom(activeRoomId, 'debug_manual_recovery');
            } else {
              console.warn('[DEBUG] No active room to recover');
            }
          }
        }
      };
    };

    // **NEW**: Debug function to test game_players RLS directly
    (window as { debugTestGamePlayersRLS?: (roomId?: string) => Promise<unknown> }).debugTestGamePlayersRLS = async (roomId?: string) => {
      const targetRoomId = roomId || 'af7d98c9-c273-4ca1-a7ae-17821942bed0'; // fresh's room from screenshot
      
      console.log(`[DEBUG] Testing game_players RLS for room: ${targetRoomId}`);
      console.log(`[DEBUG] Current user: ${user?.id}`);
      
      try {
        // Test 1: Can we see individual game_players records?
        console.log('[DEBUG] Test 1: Fetching individual game_players records...');
        const { data: playersData, error: playersError } = await supabase
          .from('game_players')
          .select('user_id, is_connected, is_ready')
          .eq('room_id', targetRoomId);
        
        console.log('[DEBUG] Test 1 Results:', { 
          playersData, 
          playersError,
          playerCount: playersData?.length || 0
        });
        
        // Test 2: Can we get count aggregation?
        console.log('[DEBUG] Test 2: Testing count aggregation...');
        const { data: countData, error: countError } = await supabase
          .from('game_players')
          .select('*', { count: 'exact', head: true })
          .eq('room_id', targetRoomId);
        
        console.log('[DEBUG] Test 2 Results:', { 
          countData, 
          countError,
          count: countData // This should be null but the count is in the response metadata
        });
        
        // Test 3: Can we use count in a join context like fetchAndSetGameRooms?
        console.log('[DEBUG] Test 3: Testing count in join context...');
        const { data: joinData, error: joinError } = await supabase
          .from('game_rooms')
          .select(`
            id,
            title,
            status,
            game_players(count)
          `)
          .eq('id', targetRoomId)
          .maybeSingle();
        
        console.log('[DEBUG] Test 3 Results:', { 
          joinData, 
          joinError,
          gamePlayersStructure: joinData?.game_players,
          extractedCount: Array.isArray(joinData?.game_players) && joinData.game_players[0]?.count
        });
        
        return {
          test1_individualRecords: { playersData, playersError, count: playersData?.length || 0 },
          test2_countAggregation: { countData, countError },
          test3_joinContext: { joinData, joinError, extractedCount: Array.isArray(joinData?.game_players) && joinData.game_players[0]?.count },
          summary: {
            canSeeIndividualRecords: !playersError && Array.isArray(playersData),
            individualRecordCount: playersData?.length || 0,
            canUseCountInJoin: !joinError && joinData?.game_players,
            joinExtractedCount: Array.isArray(joinData?.game_players) && joinData.game_players[0]?.count || 0,
            possibleRLSIssue: playersError || joinError || (playersData?.length === 0 && user?.id !== targetRoomId) // Simplified check
          }
        };
        
      } catch (e) {
        console.error('[DEBUG] Exception testing game_players RLS:', e);
        return { exception: e };
      }
    };

    // **NEW**: Debug function to compare what the host sees vs what others see
    (window as { debugComparePlayerCounts?: () => Promise<unknown> }).debugComparePlayerCounts = async () => {
      console.log('[DEBUG] Comparing player counts across all visible rooms...');
      
      try {
        // Get all rooms with individual player records (if RLS allows)
        const { data: roomsWithPlayers, error: roomsError } = await supabase
          .from('game_rooms')
          .select(`
            id,
            title,
            host_id,
            status,
            max_players,
            game_players(
              user_id,
              is_connected
            )
          `)
          .in('status', ['waiting', 'active']);
        
        // Get all rooms with count aggregation
        const { data: roomsWithCounts, error: countsError } = await supabase
          .from('game_rooms')
          .select(`
            id,
            title,
            host_id,
            status,
            max_players,
            game_players(count)
          `)
          .in('status', ['waiting', 'active']);
        
        console.log('[DEBUG] Comparison Results:', {
          roomsWithPlayers: roomsWithPlayers?.map(r => ({
            id: r.id.substring(0, 8) + '...',
            title: r.title,
            hostId: r.host_id.substring(0, 8) + '...',
            isOwnRoom: r.host_id === user?.id,
            playerRecords: r.game_players?.length || 0,
            connectedPlayers: r.game_players?.filter((p: { is_connected: boolean }) => p.is_connected).length || 0
          })),
          roomsWithCounts: roomsWithCounts?.map(r => ({
            id: r.id.substring(0, 8) + '...',
            title: r.title,
            hostId: r.host_id.substring(0, 8) + '...',
            isOwnRoom: r.host_id === user?.id,
            countAggregation: Array.isArray(r.game_players) && r.game_players[0]?.count || 0
          })),
          errors: { roomsError, countsError },
          userContext: user?.id
        });
        
        return {
          roomsWithPlayers,
          roomsWithCounts,
          roomsError,
          countsError,
          comparison: roomsWithPlayers?.map(r => {
            const countRoom = roomsWithCounts?.find(cr => cr.id === r.id);
            return {
              roomId: r.id.substring(0, 8) + '...',
              title: r.title,
              isOwnRoom: r.host_id === user?.id,
              individualRecordsCount: r.game_players?.length || 0,
              countAggregationResult: Array.isArray(countRoom?.game_players) && countRoom.game_players[0]?.count || 0,
              match: (r.game_players?.length || 0) === (Array.isArray(countRoom?.game_players) && countRoom.game_players[0]?.count || 0)
            };
          })
        };
        
      } catch (e) {
        console.error('[DEBUG] Exception comparing player counts:', e);
        return { exception: e };
      }
    };
  }

  // Layer 3: The Reactive UI - Check if current user is marked as disconnected
  const self = playersInRoom.find(p => p.user_id === user?.id);
  const handleRejoinRoom = async () => {
    if (activeRoomId) {
      await handleJoinRoom(activeRoomId);
    }
  };

  // If current user is marked as disconnected, show the DisconnectedOverlay
  // Show loading state while determining authentication status to prevent race condition
  if (isAuthLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center text-white bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-lg">Initializing...</p>
        </div>
      </div>
    );
  }

  if (self && !self.is_connected && selectedOverallGameType === 'multiplayer' && multiplayerPanelState === 'in_room') {
    return <DisconnectedOverlay onReconnect={handleRejoinRoom} />;
  }

  return (
    <main className="flex min-h-screen flex-col items-center p-4 md:p-12 lg:p-24 pt-12 md:pt-20">
      {/* Auth Modal in top-right corner */}
      <div className="absolute top-4 right-4 z-[100]">
        <AuthModal ref={authModalRef} onAuthSuccess={handleAuthSuccess} />
      </div>



      {/* Header Row Container for Title and Flanking SP Mode Buttons */}
      <div className="flex items-center justify-center w-full max-w-5xl mb-1 space-x-4 md:space-x-6">
        {/* Timed Mode Button Container (always takes up space) */}
        <div className="w-[150px] md:w-[180px] flex-shrink-0 flex justify-start">
          {selectedOverallGameType === 'single-player' && (
            <Button
              onClick={() => handleModeButtonClick('timed')}
              className={cn(
                "px-5 py-2.5 md:px-7 md:py-3.5 text-lg md:text-xl font-bold rounded-lg",
                "border-yellow-500 border-[3px]",
                "transition-all shadow-md",
                activeGameMode === 'timed' 
                  ? "bg-red-600 text-white ring-4 ring-yellow-500 ring-offset-2 ring-offset-green-950"
                  : "bg-red-700 text-gray-200 hover:bg-red-500"
              )}
            >
              Timed Mode
            </Button>
          )}
        </div>

        {/* Jumbotron Title - Centered */}
        <div
          className="relative bg-[#0A0A0A] p-3 md:p-4 shadow-2xl inline-block border-2 border-[#050505] rounded-[5px]
                     flex-shrink-0
                     [background-image:repeating-radial-gradient(circle_at_center,rgba(255,255,255,0.10)_0,rgba(255,255,255,0.10)_2px,transparent_2px,transparent_100%)]
                     [background-size:8px_8px]
                     [box-shadow:0_0_10px_rgba(0,0,0,0.5),inset_0_0_8px_rgba(80,80,80,0.2),0_2px_10px_1px_rgba(0,0,0,0.35)]
                     after:content-[''] after:absolute after:inset-0
                     after:[background:linear-gradient(rgba(10,10,10,0.2)_50%,transparent_50%)]
                     after:[background-size:100%_6px] after:opacity-50 after:pointer-events-none after:z-[1]
                     before:content-[''] before:absolute before:inset-0 before:rounded-[5px]
                     before:[box-shadow:inset_0_3px_0_0_rgba(255,255,255,0.1),inset_0_-3px_0_0_rgba(0,0,0,0.3),inset_3px_0_0_0_rgba(255,255,255,0.07),inset_-3px_0_0_0_rgba(0,0,0,0.2)]
                     before:pointer-events-none before:z-[2]"
        >
          <h1 className="jumbotron-title text-4xl md:text-5xl uppercase whitespace-nowrap">
            Recognition Combine
          </h1>
        </div>

        {/* Normal Mode Button Container (always takes up space) */}
        <div className="w-[150px] md:w-[180px] flex-shrink-0 flex justify-end">
          {selectedOverallGameType === 'single-player' && (
            <Button
              onClick={() => handleModeButtonClick('normal')}
              className={cn(
                "px-5 py-2.5 md:px-7 md:py-3.5 text-lg md:text-xl font-bold rounded-lg",
                "border-yellow-500 border-[3px]",
                "transition-all shadow-md",
                activeGameMode === 'normal' 
                  ? "bg-blue-600 text-white ring-4 ring-yellow-500 ring-offset-2 ring-offset-green-950"
                  : "bg-blue-700 text-gray-200 hover:bg-blue-600"
              )}
            >
              Normal Mode
            </Button>
          )}
        </div>
      </div>

      {/* Single-player / Multiplayer Mode Selection Buttons */}
      <div 
        className="flex items-center justify-center w-full max-w-lg mb-4 md:mb-6 space-x-0"
        style={{ marginTop: '-5px' }}
      >
        <Button
          onClick={() => handleOverallGameTypeChange('single-player')}
          className={cn(
            "px-5 py-2.5 md:px-7 md:py-3 text-sm md:text-base font-bold rounded-l-lg rounded-r-none",
            "transition-all duration-200 ease-in-out",
            selectedOverallGameType === 'single-player'
              ? "bg-black text-yellow-300 ring-2 ring-lime-500 ring-offset-2 ring-offset-green-900 brightness-95 shadow-inner shadow-lime-500/30 scale-105 z-10"
              : "bg-slate-800 text-slate-400 hover:bg-slate-700 hover:text-lime-400 border border-slate-700 shadow-md"
          )}
        >
          Single-player Mode
        </Button>
        <Button
          onClick={() => handleOverallGameTypeChange('multiplayer')}
          className={cn(
            "px-5 py-2.5 md:px-7 md:py-3 text-sm md:text-base font-bold rounded-r-lg rounded-l-none",
            "transition-all duration-200 ease-in-out",
            selectedOverallGameType === 'multiplayer'
              ? "bg-black text-yellow-300 ring-2 ring-lime-500 ring-offset-2 ring-offset-green-900 brightness-95 shadow-inner shadow-lime-500/30 scale-105 z-10"
              : "bg-slate-800 text-slate-400 hover:bg-slate-700 hover:text-lime-400 border border-slate-700 shadow-md"
          )}
        >
          Multiplayer Mode
        </Button>
      </div>

      {/* Main Content Container */}
      <div className="w-full flex flex-col items-center">
        {/* Main Grid Container - ALWAYS PRESENT with consistent spacing */}
        <div className="w-full max-w-[77rem] mx-auto grid grid-cols-1 md:grid-cols-4 gap-5" style={{ minHeight: '600px' }}>
          {/* Left Panel */}
          <div className="md:col-span-1 bg-green-900 bg-opacity-75 pt-5 px-3 pb-3 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col">
            {selectedOverallGameType === 'single-player' ? (
              // Single Player Left Panel Content
              <>
                <div className="flex flex-col items-center">
                  <div className="flex items-baseline justify-center gap-x-3 w-full">
                    <div className="text-center">
                      <h2 className="text-2xl md:text-3xl font-bold mb-0">Score</h2>
                      <span className="text-3xl md:text-4xl font-extrabold text-yellow-400 leading-none block">
                        {score}
                      </span>
                    </div>
                    {activeGameMode === 'timed' && gameStatus === 'playing' && (
                      <div className="text-center">
                        <h2 className="text-lg md:text-xl font-bold mb-0">Timer</h2>
                        <span className="text-2xl md:text-3xl font-extrabold text-orange-400 leading-none block">
                          {formattedTimer}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="mt-3 text-sm leading-tight text-center w-full">
                    {activeGameMode === 'normal' && (
                      <>
                        <p>Streak: {streak}</p>
                        <p>Best Streak: {bestStreak}</p>
                      </>
                    )}
                    <p>Best Normal: {hasMounted ? bestNormalScore : 0}</p>
                    <p>Best Timed: {hasMounted ? bestTimedScore : 0}</p>
                  </div>
                </div>
                <hr className="my-3 border-green-700" />
                <div className="flex-1 flex flex-col min-h-0 pb-2.5">
                  <h3 className="text-2xl font-semibold mb-1 text-center">Recent Answers</h3>
                  <div className="flex-1 overflow-y-auto pr-2 bg-gray-800 bg-opacity-50">
                    {activeGameMode === 'normal' ? (
                      <RecentAnswersList answers={recentAnswers} onSelect={handleRecentSelect} />
                    ) : (
                      <p className="text-gray-400 text-center mt-4">Recent answers disabled in Timed Mode.</p>
                    )}
                  </div>
                </div>
              </>
            ) : currentRoomGameData && currentRoomGameData.status === 'active' && activeRoomId && multiplayerPanelState === 'in_room' ? (
              // Multiplayer Active Game - Scores Panel
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-2 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                  Player Scores (Round {currentRoomGameData.current_round_number || 0})
                </h2>
                <div className="flex-1 overflow-y-auto space-y-1.5 pr-1 text-sm">
                  {(() => {
                    console.log('[PlayerScoresPanel_ActiveGame] Rendering scores. Players in room:', 
                      playersInRoom.map(p => ({ uid: p.user_id, name: p.profile?.username, connected: p.is_connected })),
                      'Scores from DB:', currentRoomGameData.player_scores
                    );

                    if (playersInRoom.length === 0 && currentRoomGameData.status === 'active') { 
                      return <p className="text-gray-400 italic text-center mt-4">Waiting for players to connect/appear...</p>;
                    }
                    
                    const displayPlayersWithScores = playersInRoom.map(player => {
                      const score = currentRoomGameData.player_scores?.[player.user_id] || 0;
                      const isCurrentUser = player.user_id === user?.id;
                      const isHost = player.user_id === currentRoomGameData.host_id;
                      
                      console.log('[PlayerScoresPanel_ActiveGame] Processing player:', {
                        userId: player.user_id,
                        username: player.profile?.username,
                        score,
                        isCurrentUser,
                        isHost,
                        isConnected: player.is_connected
                      });

                      return {
                        key: player.user_id,
                        userId: player.user_id,
                        username: player.profile?.username || `User...${player.user_id.slice(-4)}`,
                        score,
                        isCurrentUser,
                        isHost,
                        isConnected: player.is_connected
                      };
                    }).sort((a, b) => {
                      // Sort by score (descending), then by username (ascending)
                      if (b.score !== a.score) return b.score - a.score;
                      return a.username.localeCompare(b.username);
                    });

                    console.log('[PlayerScoresPanel_ActiveGame] Displayable players with scores:', displayPlayersWithScores);

                    // If displayPlayersWithScores is STILL empty after mapping (e.g. if playersInRoom had non-game related entries)
                    // This check is mostly defensive if playersInRoom somehow had entries that didn't map to displayable players
                    if (displayPlayersWithScores.length === 0) {
                        return <p className="text-gray-400 italic text-center mt-4">Waiting for players...</p>; // Generic fallback
                    }

                    return displayPlayersWithScores.map((pws, index) => {
                      const rank = index + 1;
                      let trophy = '';
                      if (rank === 1) trophy = '🥇';
                      else if (rank === 2) trophy = '🥈';
                      else if (rank === 3) trophy = '🥉';

                      return (
                        <div
                          key={pws.key}
                          className={cn(
                            "flex justify-between items-center p-2 rounded",
                            "bg-slate-800/70 hover:bg-slate-700/90 transition-colors",
                            !pws.isConnected && "opacity-50 italic",
                            pws.isCurrentUser && "ring-1 ring-yellow-500/50" // Highlight current user
                          )}
                        >
                          <span className="truncate flex items-center">
                            <span className="mr-2 w-5 text-center">{trophy || rank}</span>
                            {pws.username}
                            {pws.isCurrentUser && <span className="text-xs text-blue-400 ml-1">(You)</span>}
                            {pws.isHost && <span className="text-xs text-green-400 ml-1">(Host)</span>}
                            {!pws.isConnected && <span className="text-xs text-red-500 ml-1">(off)</span>}
                          </span>
                          <span className="font-semibold text-yellow-400">{pws.score} pts</span>
                        </div>
                      );
                    });
                  })()}
                </div>
              </div>
            ) : (
              // Multiplayer Lobby - Leaderboards Panel (existing logic)
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">Leaderboards</h2>
                <div className="flex-1 overflow-y-auto space-y-3 pr-1 text-sm">
                  {/* Personal Records */}
                  <div className="mb-2">
                    <h3 className="text-lg font-semibold text-lime-300 mb-1">Personal Bests</h3>
                    {personalRecords.length > 0 ? hasMounted && personalRecords.map(pr => (
                      <div key={pr.username} className="flex justify-between text-xs bg-slate-800 px-2 py-1 rounded">
                        <span>{pr.username}</span>
                        <span className="font-semibold">{pr.score}</span>
                      </div>
                    )) : <p className="text-xs text-gray-400 italic">No records yet.</p>}
                  </div>

                  {/* Global Leaderboard */}
                  <div>
                    <button 
                      onClick={() => handleExpandLeaderboard("Global Top 5", globalLeaderboard)}
                      className="text-lg font-semibold text-lime-300 mb-1 w-full text-left hover:text-yellow-300 transition-colors"
                    >
                      Global Top 5 ❯
                    </button>
                    {hasMounted && globalLeaderboard.slice(0, 3).map(entry => (
                      <div key={entry.rank + entry.username} className="flex justify-between text-xs bg-slate-800/50 px-2 py-0.5 rounded mb-0.5">
                        <span>{entry.rank}. {entry.username}</span>
                        <span className="font-semibold">{entry.score}</span>
                      </div>
                    ))}
                  </div>

                  {/* Regional Leaderboards */}
                  {userRegion && (
                    <div>
                      <h3 className="text-lg font-semibold text-lime-300 mt-2 mb-1">Regional (Your: {userRegion})</h3>
                      {hasMounted && regionalLeaderboards.map(rl => (
                        <div key={rl.regionName} className="mb-1">
                          <button 
                            onClick={() => handleExpandLeaderboard(`${rl.regionName} Top 5`, rl.entries)}
                            className="text-sm font-medium text-sky-300 w-full text-left hover:text-sky-200 transition-colors"
                          >
                            {rl.regionName} {rl.regionName === userRegion ? '(Your Region)' : ''} ❯
                          </button>
                          {hasMounted && rl.entries.slice(0,2).map(entry => (
                            <div key={entry.rank + entry.username} className="flex justify-between text-xs bg-slate-800/30 px-2 py-0.5 rounded mb-0.5">
                              <span>{entry.rank}. {entry.username}</span>
                              <span className="font-semibold">{entry.score}</span>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Center Panel */}
          <div className="relative md:col-span-2 bg-green-800 bg-opacity-80 p-4 md:p-6 rounded-lg border-2 border-green-600 text-white flex flex-col items-center justify-center min-h-[600px] h-[600px] shadow-2xl">
            {/* Animation Components */}
            {selectedOverallGameType === 'single-player' && (
              <>
                <ScorePopup scoreChange={lastScoreChange} trigger={animationTrigger} />
                <FootballFx trigger={animationTrigger} streak={streak} />
                <TimeChangePopup
                  timeChange={lastTimeChange}
                  trigger={timeChangeAnimationTrigger}
                  scoreChanged={!!lastScoreChange && lastScoreChange > 0}
                />
              </>
            )}

            {selectedOverallGameType === 'single-player' ? (
              // Single Player Center Panel Content
              <>
                {isLoadingInitialGame ? (
                  <FootballLoader message={loadingMessage} />
                ) : isCountdownActive ? (
                  <div className="text-8xl font-archivo text-yellow-300" style={{textShadow: '3px 3px 5px rgba(0,0,0,0.7)'}}>
                    {countdownValue}
                  </div>
                ) : viewingRecentPlayer ? (
                  <>
                    <PlayerImageDisplay
                      imageUrl={viewingRecentPlayer?.local_image_path ? `/players_images/${viewingRecentPlayer.local_image_path}` : '/images/placeholder.jpg'}
                      altText={viewingRecentPlayer?.player_name || 'Recent Player'}
                    />
                    <h2 className="text-3xl font-bold my-4 text-center">{viewingRecentPlayer?.player_name}</h2>
                    <Button onClick={handleReturnToGame} className="mt-7 bg-sky-600 hover:bg-sky-700 text-white font-bold shadow-lg text-lg">
                      Return to Game
                    </Button>
                  </>
                ) : gameStatus === 'playing' && currentQuestion ? (
                  <>
                    {!isAnswered && (
                      <div className="mb-2 font-archivo font-bold text-white tracking-wider whitespace-nowrap" style={{ fontSize: '1.98rem', textShadow: '2px 2px 4px rgba(0,0,0,0.5)', marginTop: '-35px' }}>
                        WHO&apos;S THIS ACTIVE NFL PLAYER?
                      </div>
                    )}
                    <PlayerImageDisplay
                      imageUrl={currentQuestion?.imageUrl || '/images/placeholder.jpg'}
                      altText={(isAnswered && activeGameMode === 'normal' && currentQuestion?.correctPlayer) ? currentQuestion.correctPlayer.player_name : 'Guess the player'}
                    />
                    <div className="grid grid-cols-2 gap-5 w-full max-w-[440px] mt-4">
                      {currentQuestion?.choices.map((choice, index) => {
                        const choiceKey = `${currentQuestion?.correctPlayer?.id || 'unknown'}-${choice.name}-${index}`;
                        const wasChosen = isAnswered && userChoiceName === choice.name;

                        return (
                          <ChoiceButton
                            key={choiceKey}
                            choiceText={choice.name}
                            onClick={() => submitAnswer(choice.name)}
                            disabled={isAnswered}
                            isCorrect={(isAnswered && activeGameMode === 'normal') ? choice.isCorrect : null}
                            wasChosen={wasChosen}
                          />
                        );
                      })}
                    </div>
                    {isAnswered && activeGameMode === 'normal' && (
                      <Button onClick={handleNextQuestionClick} className="mt-7 bg-yellow-500 hover:bg-yellow-600 text-black font-bold shadow-lg text-lg">
                        Next Question
                      </Button>
                    )}
                  </>
                ) : gameStatus === 'finished' && (
                  <div className="flex flex-col items-center justify-center h-full">
                    <h2 className="text-4xl font-bold mb-2 text-yellow-300">Game Over!</h2>
                    <p className="text-2xl mb-4">Your {activeGameMode === 'timed' ? 'Timed Mode' : ''} Score: {score}</p>
                    {activeGameMode === 'timed' && <p className="text-lg mb-4">Best Timed: {bestTimedScore}</p>}
                    {activeGameMode === 'normal' && <p className="text-lg mb-4">Best Normal: {bestNormalScore}</p>}
                    <Button onClick={handleGameResetClick} className="bg-lime-600 hover:bg-lime-700 text-black font-bold shadow-lg text-lg px-6 py-3">
                      Play {activeGameMode === 'timed' ? 'Timed Mode' : 'Normal Mode'} Again
                    </Button>
                  </div>
                )}
              </>
            ) : centerPanelMpState === 'expanded_leaderboard' && expandedLeaderboardData ? (
              // Expanded Leaderboard View
              <div className="w-full h-full flex flex-col p-2">
                <div className="flex justify-between items-center mb-3">
                  <h2 className="text-3xl font-bold text-yellow-300">{expandedLeaderboardData?.title || 'Leaderboard'}</h2>
                  <Button 
                    onClick={() => { setExpandedLeaderboardData(null); setCenterPanelMpState('lobby_list_detail');}} 
                    className="bg-slate-700 hover:bg-slate-600 text-xs py-1 px-2"
                  >
                    Back to Lobby
                  </Button>
                </div>
                <div className="flex-1 overflow-y-auto bg-slate-900/50 p-3 rounded-md">
                  {hasMounted && expandedLeaderboardData?.entries.map(entry => (
                    <div key={entry.rank + entry.username} className="flex justify-between items-center p-2 border-b border-slate-700 hover:bg-slate-700/50 rounded">
                      <span className="text-lg">{entry.rank}. {entry.username}</span>
                      <span className="text-xl font-semibold text-yellow-400">{entry.score}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) : centerPanelMpState === 'lobby_list_detail' ? (
              <div className="w-full h-full flex flex-col items-center justify-start p-2">
                <div className="flex justify-between items-center mb-2 w-full">
                  <h2 className="text-3xl font-bold text-yellow-300">
                    {selectedRoomForDetail ? `Room: ${selectedRoomForDetail.title || ('...' + selectedRoomForDetail.id.slice(-6))}` : "Game Lobby"}
                  </h2>
                  {!selectedRoomForDetail && (
                    <div className="flex gap-2">
                      <Button
                        onClick={async () => {
                          console.log("[Client] Refresh List button clicked.");
                          await fetchAndSetGameRooms();
                        }}
                        disabled={isLoadingRooms}
                        className="px-3 py-1 text-xs bg-sky-600 hover:bg-sky-700 text-white rounded-md shadow-md"
                      >
                        {isLoadingRooms ? 'Refreshing...' : 'Refresh List'}
                      </Button>
                    </div>
                  )}
                </div>
                <ConnectionStatusIndicator status={connectionStatus} />
                {errorMp && !selectedRoomForDetail && (
                  <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-3 text-center w-full">{errorMp}</p>
                )}
                
                {lobbyFetchError && !selectedRoomForDetail && !isLoadingRooms && (
                  <div className="text-center text-red-400 bg-red-900/50 p-3 rounded text-xs mb-3 w-full">
                    <p>Could not load games. Please try again.</p>
                    <Button
                      onClick={async () => {
                        console.log("[Client] Retry fetch button clicked after error.");
                        await fetchAndSetGameRooms();
                      }}
                      className="mt-2 px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded-md"
                    >
                      Retry
                    </Button>
                  </div>
                )}

                {selectedRoomForDetail ? (
                  <div className="w-full bg-slate-800/60 p-4 rounded-lg shadow-lg text-sm">
                    <p><span className="font-semibold text-lime-300">Mode:</span> <span className="capitalize">{selectedRoomForDetail.multiplayer_mode?.replace('_',' ')}</span></p>
                    <p><span className="font-semibold text-lime-300">Host:</span> {selectedRoomForDetail.profiles?.username || 'Unknown Host'}</p>
                    <p><span className="font-semibold text-lime-300">Status:</span> <span className="capitalize">{selectedRoomForDetail.status}</span></p>
                    <p>
                      <span className="font-semibold text-lime-300">Players:</span> {selectedRoomForDetail.connected_players ?? 0} / {selectedRoomForDetail.max_players ?? 8}
                    </p>
                    <p className="mt-1"><span className="font-semibold text-lime-300">Code:</span> {selectedRoomForDetail.room_code || 'N/A'}</p>
                    
                    {(() => {
                      // DEBUG: Log authentication state for debugging race conditions
                      console.log('[AUTH_DEBUG] Room detail authentication check:', {
                        hasUser: !!user,
                        userId: user?.id,
                        isAuthLoading,
                        selectedRoomId: selectedRoomForDetail?.id,
                        timestamp: new Date().toISOString()
                      });
                      return null;
                    })()}
                    
                    {user ? (() => {
                      // ENHANCED LOGIC: More precise determination of user's relationship to this room
                      console.log('[LobbyDetail] ENHANCED room relationship analysis:', {
                        roomId: selectedRoomForDetail.id,
                        roomStatus: selectedRoomForDetail.status,
                        userId: user.id,
                        roomHostId: selectedRoomForDetail.host_id,
                        isCurrentUserHost: selectedRoomForDetail.host_id === user.id,
                        currentActiveRoomId: activeRoomId,
                        isActiveRoomMatchingDetailRoom: activeRoomId === selectedRoomForDetail.id,
                        playersInDetailRoomGamePlayers: selectedRoomForDetail.game_players?.length || 0,
                        userInDetailRoomGamePlayers: selectedRoomForDetail.game_players?.some(gp => gp.user_id === user.id) || false,
                        currentPlayersInRoomCount: playersInRoom.length,
                        userInCurrentPlayersInRoom: playersInRoom.some(p => p.user_id === user.id),
                        clientSideConnectionConsistency: {
                          activeRoomIdSet: !!activeRoomId,
                          playersInRoomHasUser: playersInRoom.some(p => p.user_id === user.id),
                          activeRoomMatchesDetailRoom: activeRoomId === selectedRoomForDetail.id,
                          consistencyCheck: !activeRoomId || (activeRoomId === selectedRoomForDetail.id && playersInRoom.some(p => p.user_id === user.id))
                        },
                        timestamp: new Date().toISOString()
                      });

                      // Find if the current logged-in user has an entry in the game_players list of the room being detailed
                      const playerEntryInDetailRoom = selectedRoomForDetail.game_players?.find(
                        (gp) => gp.user_id === user.id
                      );

                      // CRITICAL FIX: Check if user was an original player (for reconnection after host migration)
                      const wasOriginalPlayer = selectedRoomForDetail.original_player_ids?.includes(user.id) || false;
                      
                      // CRITICAL FIX: Determine if user is "actively connected" to this specific room
                      // This should only be true if:
                      // 1. activeRoomId matches this room's ID AND
                      // 2. playersInRoom includes the current user AND
                      // 3. The user actually joined/created this room in the current session
                      const isActivelyConnectedToThisRoom = 
                        activeRoomId === selectedRoomForDetail.id && 
                        playersInRoom.some(p => p.user_id === user.id) &&
                        multiplayerPanelState === 'in_room'; // Additional check: user should be in "in_room" state
                      
                      console.log('[LobbyDetail] Connection status determination:', {
                        playerEntryInDetailRoom: !!playerEntryInDetailRoom,
                        playerEntryConnected: playerEntryInDetailRoom?.is_connected,
                        wasOriginalPlayer,
                        originalPlayerIds: selectedRoomForDetail.original_player_ids,
                        isActivelyConnectedToThisRoom,
                        rationale: {
                          activeRoomIdMatches: activeRoomId === selectedRoomForDetail.id,
                          userInPlayersInRoom: playersInRoom.some(p => p.user_id === user.id),
                          inRoomState: multiplayerPanelState === 'in_room',
                          allConditionsMet: isActivelyConnectedToThisRoom
                        }
                      });

                      if (selectedRoomForDetail.status === 'waiting') {
                        // Room is WAITING
                        if (isActivelyConnectedToThisRoom) {
                          // User is ACTIVELY connected to this waiting room (should not normally show detail view)
                          console.log('[LobbyDetail] User is actively connected to this waiting room - showing status message');
                          return <p className="text-center text-gray-400 mt-3">You are currently in this waiting room.</p>;
                        } else if (playerEntryInDetailRoom && !playerEntryInDetailRoom.is_connected) {
                          // User has an entry but is disconnected -> "Rejoin Waiting Room"
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-orange-500 hover:bg-orange-600 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Waiting Room"}
                            </Button>
                          );
                        } else if (!playerEntryInDetailRoom) {
                          // User has no entry in this waiting room -> "Join This Room"
                          const isFull = selectedRoomForDetail.connected_players != null &&
                                        selectedRoomForDetail.max_players != null &&
                                        selectedRoomForDetail.connected_players >= selectedRoomForDetail.max_players;
                          return (
                            <Button
                              onClick={() => {
                                console.log('[JOIN_BUTTON] Join button clicked:', {
                                  hasUser: !!user,
                                  userId: user?.id,
                                  isAuthLoading,
                                  timestamp: new Date().toISOString()
                                });
                                handleJoinRoom(selectedRoomForDetail);
                              }}
                              className="w-full mt-3 bg-blue-600 hover:bg-blue-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                              disabled={!user || isAuthLoading || isFull || isJoiningOrRejoiningRoom}
                            >
                              {isJoiningOrRejoiningRoom ? "Joining..." : isFull ? "Room Full" : "Join This Room"}
                            </Button>
                          );
                        } else {
                          // playerEntryInDetailRoom exists and is connected, but user is not actively connected client-side
                          // This might be a data inconsistency - show rejoin option
                          console.log('[LobbyDetail] Data inconsistency detected: user has connected DB entry but no active client connection');
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-orange-500 hover:bg-orange-600 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Reconnecting..." : "Reconnect to Room"}
                            </Button>
                          );
                        }
                      } else if (selectedRoomForDetail.status === 'active') {
                        // Room is ACTIVE
                        const isCurrentUserHost = selectedRoomForDetail.host_id === user.id;
                        
                        if (isActivelyConnectedToThisRoom) {
                          // Client is actively connected to this active room
                          console.log('[LobbyDetail] Client is actively connected to this active room');
                          return <p className="text-center text-gray-400 mt-3">You are currently in this active game.</p>;
                        } else if (playerEntryInDetailRoom && !playerEntryInDetailRoom.is_connected) {
                          // User was part of this active game and is disconnected -> "Rejoin Active Game"
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-green-600 hover:bg-green-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Active Game"}
                            </Button>
                          );
                        } else if (playerEntryInDetailRoom && playerEntryInDetailRoom.is_connected && !isActivelyConnectedToThisRoom) {
                          // User is part of this active game, DB shows connected, BUT client is NOT actively connected
                          // This happens after logout/login - need to re-establish client connection
                          const buttonText = isCurrentUserHost ? "Re-enter Your Game" : "Rejoin Active Game";
                          const buttonColor = isCurrentUserHost ? "bg-blue-600 hover:bg-blue-700" : "bg-green-600 hover:bg-green-700";
                          
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail)}
                              disabled={isJoiningOrRejoiningRoom}
                              className={`w-full mt-3 ${buttonColor} py-1.5 disabled:opacity-50 disabled:cursor-not-allowed`}
                            >
                              {isJoiningOrRejoiningRoom ? (isCurrentUserHost ? "Re-entering..." : "Rejoining...") : buttonText}
                            </Button>
                          );
                        } else if (!playerEntryInDetailRoom && isCurrentUserHost) {
                          // Host is viewing their own active game but has NO player entry
                          // This happens when host used "Leave Game" and leave-room-handler deleted their record
                          console.log('[LobbyDetail] Host is viewing their active game with no player entry. Offering RE-ENTER option.');
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-blue-600 hover:bg-blue-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Re-entering..." : "Re-enter Your Game"}
                            </Button>
                          );
                        } else if (!playerEntryInDetailRoom && wasOriginalPlayer) {
                          // CRITICAL FIX: User was an original player but is no longer in game_players (after host migration)
                          // This happens when the original host left and was removed from game_players
                          console.log('[LobbyDetail] Original player viewing active game with no current player entry. Offering REJOIN option.');
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-green-600 hover:bg-green-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Active Game"}
                            </Button>
                          );
                        } else {
                          // Game is active, and user was NOT part of it AND is not the host AND was not an original player
                          console.log('[LobbyDetail] Non-participant viewing an active game with no player entry');
                          return <p className="text-center text-yellow-400 mt-3">Game in progress. Cannot join.</p>;
                        }
                      } else if (selectedRoomForDetail.status === 'finished') {
                        // Room is FINISHED
                        return <p className="text-center text-gray-400 mt-3">This game has finished.</p>;
                      }

                      return null; // Default: no button if other conditions aren't met
                    })() : isAuthLoading ? (
                      <p className="text-center text-gray-400 mt-3">Loading authentication...</p>
                    ) : (
                      <p className="text-center text-gray-400 mt-3">Please sign in to join rooms.</p>
                    )}
                    
                    {/* "Back to List" Button - always shown when selectedRoomForDetail is active */}
                    <Button 
                      onClick={() => {
                        console.log('[BackToList] Navigating back to lobby list from room detail view');
                        setSelectedRoomForDetail(null);
                        setMultiplayerPanelState('lobby_list');
                        setCenterPanelMpState('lobby_list_detail');
                      }} 
                      className="w-full mt-2 bg-gray-600 hover:bg-gray-500 text-xs py-1"
                    >
                      Back to List
                    </Button>
                  </div>
                ) : (
                  <div className="w-full flex-1 overflow-y-auto space-y-2 pr-1">
                    {(() => {
                      console.log("[RoomList Render Attempt] isLoadingRooms:", isLoadingRooms, "gameRooms:", gameRooms, "gameRooms.length:", gameRooms?.length);
                      return null;
                    })()}

                    {isLoadingRooms && (
                      <p className="text-center text-gray-300 pt-5">Loading available games...</p>
                    )}
                    
                    {!isLoadingRooms && gameRooms && gameRooms.length === 0 && (
                      <p className="text-center text-gray-300 pt-5">No public games available. Create one!</p>
                    )}

                    {!isLoadingRooms && gameRooms && gameRooms.length > 0 && (
                      hasMounted && gameRooms.map(room => (
                        <div
                          key={room.id}
                          className="bg-slate-800 p-3 rounded-lg shadow-lg border-2 border-transparent hover:border-yellow-500 cursor-pointer transition-all"
                          onClick={() => handleViewLobbyDetail(room)}
                          onDoubleClick={() => user && !isAuthLoading ? handleJoinRoom(room) : null}
                        >
                          <div className="flex justify-between items-center mb-1">
                            <h4 className="text-md font-bold text-yellow-400 truncate">
                              {room.title || `Room ${room.id.slice(-6)}`}
                            </h4>
                            <span className="text-xs bg-sky-600 px-1.5 py-0.5 rounded capitalize">
                              {room.multiplayer_mode === 'competitive' ? (
                                <span className="text-xs text-yellow-400">Competitive</span>
                              ) : (
                                <span className="text-xs text-blue-400">Cooperative</span>
                              )}
                            </span>
                          </div>
                          <p className="text-xs text-gray-300 truncate">
                            Host: {room.profiles?.username || 'Unknown'} • Players: {room.connected_players ?? 0}/{room.max_players ?? 8}
                          </p>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>
            ) : centerPanelMpState === 'mp_game_active' && activeRoomId ? (
              // Active Multiplayer Game
              <div>
                {(() => {
                  // Check if we're in a server-controlled transition period
                  const isInTransition = currentRoomGameData?.transition_until && 
                    new Date(currentRoomGameData.transition_until) > new Date();
                  
                  // During transition, just show the current question UI (no special transition screen)
                  // The game will automatically advance after 3 seconds
                  
                  // First, ensure all necessary data exists and has the correct type
                  if (
                    currentRoomGameData &&
                    currentRoomGameData.status === 'active' &&
                    currentRoomGameData.current_question_data &&
                    typeof currentRoomGameData.current_question_data.questionId === 'string' &&
                    Array.isArray(currentRoomGameData.current_question_data.choices)
                  ) {
                    // If all checks pass, we can safely access the properties
                    const questionData = currentRoomGameData.current_question_data;

                    return (
                      <div className="flex flex-col items-center relative">
                        <div className="mb-2 font-archivo font-bold text-white tracking-wider whitespace-nowrap" style={{ fontSize: '1.98rem', textShadow: '2px 2px 4px rgba(0,0,0,0.5)', marginTop: '-35px' }}>
                          WHO&apos;S THIS ACTIVE NFL PLAYER?
                        </div>
                        <PlayerImageDisplay
                          imageUrl={questionData.imageUrl || '/images/placeholder.jpg'}
                          altText="Guess the player"
                        />
                        <div className="grid grid-cols-2 gap-5 w-full max-w-[440px] mt-4">
                          {hasMounted && questionData.choices.map((choice: PlayerChoice, index: number) => {
                            const wasChosen = submittedAnswerForQuestion === choice.name;
                            const showResults = hasSubmittedCurrentRound;
                            
                            return (
                              <ChoiceButton
                                key={`${questionData.questionId}-${index}`}
                                choiceText={choice.name}
                                onClick={() => {
                                  console.log('[CHOICE_BUTTON_CLICK] Button clicked:', {
                                    choice: choice.name,
                                    hasSubmittedCurrentRound,
                                    submittedAnswerForQuestion,
                                    disabled: hasSubmittedCurrentRound
                                  });
                                  handleMultiplayerAnswerSubmit(choice.name);
                                }}
                                disabled={hasSubmittedCurrentRound}
                                isCorrect={showResults ? choice.isCorrect : null}
                                wasChosen={wasChosen}
                              />
                            );
                          })}
                        </div>

                        {/* Answer submission status - positioned below buttons */}
                        {hasSubmittedCurrentRound && (
                          <div className="mt-4 text-center">
                            <div className="inline-block bg-slate-900/90 px-4 py-2 rounded-lg border border-slate-700">
                              <p className="text-green-400 text-sm font-medium">
                                ✓ Answer submitted! Waiting for other players...
                              </p>
                            </div>
                          </div>
                        )}
                        
                        {/* Debug info - remove in production */}
                        {process.env.NODE_ENV === 'development' && (
                          <div className="mt-2 text-xs text-gray-500 text-center">
                            hasSubmitted: {String(hasSubmittedCurrentRound)} | 
                            submitted: {submittedAnswerForQuestion || 'null'} |
                            qId: {questionData?.questionId?.slice(-6) || 'null'}
                          </div>
                        )}


                      </div>
                    );
                  } else if (currentRoomGameData?.status === 'waiting') {
                    return (
                      <div className="text-center">
                        <h2 className="text-3xl font-bold text-yellow-300">Waiting for game to start...</h2>
                        <p className="mt-2">Room: {currentRoomGameData.title || `...${activeRoomId?.slice(-6)}`}</p>
                      </div>
                    );
                  } else {
                    return (
                      <div className="text-center">
                        <h2 className="text-4xl font-bold text-yellow-300">Game Lobby Active</h2>
                        <p className="text-xl mt-4">Waiting for game data or game has ended.</p>
                      </div>
                    );
                  }
                })()}
              </div>
            ) : (
              <div className="text-gray-400">Error: Invalid multiplayer state.</div>
            )}
          </div>

          {/* Right Panel */}
          <div className="md:col-span-1 bg-green-900 bg-opacity-75 pt-5 px-3 pb-3 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col">
            {selectedOverallGameType === 'single-player' ? (
              <PlayerInfoPanel player={playerToShow} />
            ) : multiplayerPanelState === 'lobby_list' ? (
              // Lobby Actions / Create Room
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">Lobby Actions</h2>
                <ConnectionStatusIndicator status={connectionStatus} />
                {errorMp && centerPanelMpState === 'lobby_list_detail' && !selectedRoomForDetail && (
                  <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-2 text-center">{errorMp}</p>
                )}

                <div className="p-3 bg-slate-800/50 rounded-md border border-slate-700 mb-4">
                  <h3 className="text-lg font-semibold mb-2 text-center text-lime-300">Create New Game</h3>
                  
                  {/* Game Mode Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Game Mode:</p>
                    <div className="flex justify-around space-x-2">
                      <div>
                        <input 
                          type="radio" 
                          id="competitive_lobby" 
                          name="mpModeLobby" 
                          value="competitive" 
                          checked={newRoomMode === 'competitive'} 
                          onChange={() => setNewRoomMode('competitive')} 
                          className="mr-1 accent-yellow-400"
                        />
                        <label htmlFor="competitive_lobby" className="text-gray-200 text-xs cursor-pointer">Competitive</label>
                      </div>
                      <div>
                        <input 
                          type="radio" 
                          id="cooperative_lobby" 
                          name="mpModeLobby" 
                          value="cooperative" 
                          checked={newRoomMode === 'cooperative'} 
                          onChange={() => setNewRoomMode('cooperative')} 
                          className="mr-1 accent-yellow-400"
                        />
                        <label htmlFor="cooperative_lobby" className="text-gray-200 text-xs cursor-pointer">Cooperative</label>
                      </div>
                    </div>
                  </div>

                  {/* Game Duration Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Game Duration:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {[30, 60, 90, 120].map((duration) => (
                        <div key={duration} className="flex items-center">
                          <input
                            type="radio"
                            id={`duration_${duration}`}
                            name="gameDuration"
                            value={duration}
                            checked={selectedGameDuration === duration}
                            onChange={() => setSelectedGameDuration(duration as GameDuration)}
                            className="mr-1 accent-yellow-400"
                          />
                          <label htmlFor={`duration_${duration}`} className="text-gray-200 text-xs cursor-pointer">
                            {duration} seconds
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Max Players Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Max Players:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {[2, 4, 6, 8].map((max) => (
                        <div key={max} className="flex items-center">
                          <input
                            type="radio"
                            id={`max_players_${max}`}
                            name="maxPlayers"
                            value={max}
                            checked={selectedMaxPlayers === max}
                            onChange={() => setSelectedMaxPlayers(max as MaxPlayers)}
                            className="mr-1 accent-yellow-400"
                          />
                          <label htmlFor={`max_players_${max}`} className="text-gray-200 text-xs cursor-pointer">
                            {max} Players
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Room Title (Optional) */}
                  <div className="mb-3 text-sm">
                    <label htmlFor="roomTitle" className="block text-xs font-medium text-gray-300 mb-0.5">Room Name (optional):</label>
                    <p className="text-xs text-gray-400 mt-0.5">Default: {(userProfile?.username || "Your") + "'s Game"}</p>
                  </div>

                  {/* Create Room Button */}
                  <Button
                    onClick={handleCreateRoom}
                    disabled={!user || isLoadingRooms || isCreatingRoom || !isOnline}
                    title={!isOnline ? "You are offline. Please check your connection." : "Create a new game"}
                    className={cn(
                      "w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-1.5 text-sm rounded shadow-md",
                      "transition-all duration-200",
                      isCreatingRoom && "opacity-75 cursor-not-allowed"
                    )}
                  >
                    {(() => {
                      // Debug: Log the Host Game button state
                      const isDisabled = !user || isLoadingRooms || isCreatingRoom || !isOnline;
                      console.log('[HOST_GAME_BUTTON_DEBUG] Button state check:', {
                        isDisabled,
                        hasUser: !!user,
                        userId: user?.id,
                        isLoadingRooms,
                        isCreatingRoom,
                        isOnline,
                        userObject: user ? { id: user.id, email: user.email } : null,
                        timestamp: new Date().toISOString()
                      });
                      return isCreatingRoom ? "Creating..." : isLoadingRooms ? "..." : "Host Game";
                    })()}
                  </Button>
                </div>

                <p className="text-xs text-center text-gray-400 mb-3">
                  View available games in the center panel. Click to see details, double-click to join.
                </p>
                <div className="mt-auto border-t border-slate-700 pt-2">
                  <p className="text-xs text-gray-500 text-center">Tip: Click a room in the center to see details, then join.</p>
                </div>
              </div>
            ) : multiplayerPanelState === 'in_room' && activeRoomId ? (
              // In-Room Info / Player List OR Round Submissions
              <div className="flex flex-col h-full">
                {/* Conditional rendering based on game status */}
                {currentRoomGameData?.status === 'waiting' ? (
                  <>
                    {/* ----- WAITING STATE UI ----- */}
                    <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                      Room: {currentRoomGameData.title || `...${activeRoomId.slice(-6)}`}
                    </h2>
                    <ConnectionStatusIndicator status={connectionStatus} />
                    {errorMp && (
                      <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-2 text-center">{errorMp}</p>
                    )}
                    <div className="flex-1 bg-slate-800/50 p-2 rounded mb-2 overflow-y-auto">
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm text-lime-300">
                          {(() => {
                            // **CRITICAL: Log exactly what data is being used for the player count display**
                            const displayText = `Players (${playersInRoom.length}/${currentRoomGameData.max_players || 8})`;
                            console.log('[RENDER HOST] [PLAYER_COUNT_DISPLAY] Rendering player count display:', {
                              displayText,
                              playersInRoomLength: playersInRoom.length,
                              maxPlayers: currentRoomGameData.max_players,
                              playersInRoomActualData: JSON.parse(JSON.stringify(playersInRoom)),
                              timestamp: new Date().toISOString()
                            });
                            return displayText;
                          })()}:
                        </p>
                        {/* Show "Ready Up" button only if game is waiting */}
                        {user && playersInRoom.some(p => p.user_id === user.id) && (
                          <Button
                            onClick={handleToggleReady}
                            disabled={isSubmittingReady || connectionStatus === 'OFFLINE' || !isOnline}
                            title={
                              !isOnline
                                ? "You are offline. Please check your connection."
                                : connectionStatus === 'RECONNECTING'
                                  ? "Connection is recovering. You can still ready up."
                                  : connectionStatus === 'OFFLINE'
                                    ? "Connection lost. Please refresh the page."
                                    : ""
                            }
                            className={cn(
                              "text-xs px-2 py-1 transition-colors",
                              (isSubmittingReady || connectionStatus === 'OFFLINE' || !isOnline)
                                ? "bg-gray-400 cursor-not-allowed"
                                : playersInRoom.find(p => p.user_id === user.id)?.is_ready
                                  ? "bg-green-600 hover:bg-green-700"
                                  : "bg-yellow-600 hover:bg-yellow-700"
                            )}
                          >
                            {isSubmittingReady
                              ? "Processing..."
                              : connectionStatus === 'RECONNECTING'
                                ? "Reconnecting..."
                                : playersInRoom.find(p => p.user_id === user.id)?.is_ready
                                  ? "Ready ✓"
                                  : "Ready Up"}
                          </Button>
                        )}
                      </div>
                      {isLoadingPlayers ? (
                        <p className="text-xs text-gray-400 italic">Loading players...</p>
                      ) : playersInRoom.length === 0 ? (
                        <p className="text-xs text-gray-400 italic">Waiting for players...</p>
                      ) : (
                        (() => {
                          // **CRITICAL: Log exactly what players are being rendered in the list**
                          console.log('[RENDER HOST] [PLAYER_LIST_RENDER] About to render player list:', {
                            playersCount: playersInRoom.length,
                            playersToRender: playersInRoom.map(p => ({
                              userId: p.user_id,
                              username: p.profile?.username,
                              isReady: p.is_ready,
                              isConnected: p.is_connected
                            })),
                            timestamp: new Date().toISOString()
                          });
                          return playersInRoom.map(player => (
                            <div
                              key={player.user_id}
                              className={cn(
                                "text-xs p-1.5 rounded mb-1 flex justify-between items-center",
                                "bg-slate-700/50 hover:bg-slate-700/70",
                                "transition-all duration-200 ease-in-out", // Smooth transitions for all state changes
                                player.user_id === user?.id && "bg-slate-600/70", // Highlight current user
                                // Layer 3: The Reactive UI - functionally apparent disconnection
                                !player.is_connected && "opacity-50 grayscale"
                              )}
                              title={!player.is_connected ? `${player.profile?.username || 'Player'} is disconnected` : ''}
                            >
                              <span className="truncate">
                                {player.profile?.username || `Player...${player.user_id.slice(-4)}`}
                                {player.user_id === user?.id && " (You)"}
                                {currentRoomGameData?.host_id === player.user_id && " (Host)"}
                                {!player.is_connected && <span className="ml-1 text-red-400">🔌</span>}
                              </span>
                              {player.is_ready === true && (
                                <span className="text-green-400 ml-2">✓</span>
                              )}
                            </div>
                          ));
                        })()
                      )}
                    </div>

                    {/* Show "Start Game" button only if user is host and game is waiting */}
                    {user && currentRoomGameData?.host_id === user.id && currentRoomGameData?.status === 'waiting' && (
                      <Button
                        onClick={handleStartGame}
                        disabled={
                          isStartingGame ||
                          !playersInRoom.every(p => p.is_ready) ||
                          playersInRoom.length < 2 || // Assuming min 2 players to start
                          connectionStatus === 'OFFLINE' || // Only disable if completely offline, not during reconnecting
                          !isOnline
                        }
                        title={
                          !isOnline
                            ? "You are offline. Please check your connection."
                            : connectionStatus === 'RECONNECTING'
                              ? "Connection is recovering. You can still start the game."
                              : connectionStatus === 'OFFLINE'
                                ? "Connection lost. Please refresh the page."
                                : ""
                        }
                        className={cn(
                          "w-full mt-2 mb-2 py-2 text-lg font-bold",
                          "bg-green-600 hover:bg-green-700 text-white",
                          "transition-all duration-200 ease-in-out", // Smooth transitions for opacity/color changes
                          (isStartingGame || !playersInRoom.every(p => p.is_ready) || playersInRoom.length < 2 || connectionStatus === 'OFFLINE' || !isOnline) && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        {(() => {
                          // **CRITICAL: Log exactly what text is being rendered on the Start Game button**
                          const playerCount = playersInRoom.length;
                          const needMorePlayers = playerCount < 2;
                          const playersNeeded = needMorePlayers ? 2 - playerCount : 0;
                          const allReady = playersInRoom.every(p => p.is_ready);
                          
                          const buttonText = isStartingGame
                            ? "Starting Game..."
                            : needMorePlayers
                              ? `Need ${playersNeeded} more player(s)`
                              : !allReady
                                ? "Waiting for all to ready..."
                                : "Start Game";
                          
                          console.log('[RENDER HOST] [START_BUTTON_TEXT] Rendering Start Game button text:', {
                            buttonText,
                            playerCount,
                            needMorePlayers,
                            playersNeeded,
                            allReady,
                            isStartingGame,
                            playersInRoomData: JSON.parse(JSON.stringify(playersInRoom)),
                            timestamp: new Date().toISOString()
                          });
                          
                          return buttonText;
                        })()}
                      </Button>
                    )}
                    <div className="mt-auto border-t border-slate-700 pt-2">
                      <p className="text-xs text-gray-500 text-center">
                        {(() => {
                          // **CRITICAL: Log exactly what data is being used for the bottom status message**
                          const isHost = user && currentRoomGameData?.host_id === user.id;
                          const playerCount = playersInRoom.length;
                          const needMorePlayers = playerCount < 2;
                          const playersNeeded = needMorePlayers ? 2 - playerCount : 0;
                          const allReady = playersInRoom.every(p => p.is_ready);

                          const statusText = isHost
                            ? (needMorePlayers ? `Need ${playersNeeded} more players to start.` : !allReady ? "Waiting for players to ready up." : "Ready to start game!")
                            : "Waiting for host to start...";

                          console.log('[RENDER HOST] [BOTTOM_STATUS_TEXT] Rendering bottom status text:', {
                            statusText,
                            isHost,
                            playerCount,
                            needMorePlayers,
                            playersNeeded,
                            allReady,
                            playersInRoomData: JSON.parse(JSON.stringify(playersInRoom)),
                            timestamp: new Date().toISOString()
                          });

                          return statusText;
                        })()}
                      </p>
                      <Button
                        onClick={handleLeaveRoom}
                        className="w-full mt-3 bg-red-600 hover:bg-red-700"
                      >
                        Leave Game
                      </Button>
                    </div>
                  </>
                ) : currentRoomGameData?.status === 'active' ? (
                  <>
                    {/* ----- ACTIVE GAME STATE UI (Round Submissions Only) ----- */}
                    <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                      Round Submissions
                    </h2>
                    <div className="flex-1 space-y-1.5 max-h-[calc(100%-60px)] overflow-y-auto pr-1">
                      {allAnswers.length === 0 ? (
                        <p className="text-gray-400 italic text-center mt-4">No submissions yet...</p>
                      ) : (
                        hasMounted && allAnswers.map((answer) => {
                          // Define a stable key and check if this answer should be animating
                          const answerKey = `${answer.userId}-${answer.questionId}`;
                          const isAnimating = animatingAnswers.has(answerKey);

                          const player = playersInRoom.find(p => p.user_id === answer.userId);

                          // Improved player name lookup with fallbacks
                          let playerName = player?.profile?.username;
                          if (!playerName && answer.userId === user?.id && userProfile?.username) {
                            // Current user fallback - use their profile username
                            playerName = userProfile.username;
                          }
                          if (!playerName) {
                            playerName = `User...${answer.userId.slice(-4)}`;
                          }

                          // Get enhanced animation data for the football effect
                          const enhancedAnimationData = enhancedAnimatedAnswers[answer.userId];

                          // Debug football animation condition
                          if (answer.isCorrect) {
                            console.log('[FOOTBALL_ANIMATION_DEBUG] Checking animation conditions:', {
                              userId: answer.userId.substring(0, 8) + '...',
                              hasEnhancedData: !!enhancedAnimationData,
                              enhancedData: enhancedAnimationData,
                              hasLanded: landedAnswers.has(answerKey),
                              answerKey,
                              willShowAnimation: !!(enhancedAnimationData && landedAnswers.has(answerKey))
                            });
                          }

                          console.log('[CARD_COLOR_DEBUG] Rendering username card for answer:', {
                            userId: answer.userId.substring(0, 8) + '...',
                            isCurrentUser: answer.userId === user?.id,
                            choiceName: answer.choiceName,
                            isCorrect: answer.isCorrect,
                            isOptimistic: answer.isOptimistic,
                            willShowGreen: answer.isOptimistic ? answer.isCorrect : answer.isCorrect,
                            willShowRed: answer.isOptimistic ? !answer.isCorrect : !answer.isCorrect
                          });

                          return (
                            <div
                              // MODIFIED: Use the stable key
                              key={answerKey}
                              data-answer-key={answerKey}
                              className={cn(
                                "text-xs p-1.5 rounded relative flex justify-center items-center",
                                // MODIFIED: Animation class is now conditional
                                isAnimating && "animate-slide-up",
                                answer.isOptimistic
                                  ? answer.isCorrect
                                    ? "bg-green-600/50 border border-green-400/70 shadow-[0_0_8px_1px_rgba(77,255,77,0.3)]"
                                    : "bg-red-600/50 border border-red-400/70"
                                  : answer.isCorrect
                                    ? "bg-green-600/70 border border-green-400 shadow-[0_0_8px_1px_rgba(77,255,77,0.5)]"
                                    : "bg-red-600/70 border border-red-400"
                              )}
                              // MODIFIED: This now ONLY cleans up the slide-in animation.
                              onAnimationEnd={(e) => {
                                if (e.animationName === 'slide-up') {
                                  console.log('[FOOTBALL_ANIMATION_DEBUG] Slide-up animation ended for:', {
                                    answerKey,
                                    userId: answer.userId.substring(0, 8),
                                    isCurrentUser: answer.userId === user?.id,
                                    isCorrect: answer.isCorrect
                                  });
                                  
                                  // Stop this answer from re-animating on subsequent renders
                                  setAnimatingAnswers(prev => {
                                    const newSet = new Set(prev);
                                    newSet.delete(answerKey);
                                    return newSet;
                                  });
                                  
                                  // SIMPLIFIED: Trigger football animation directly for correct answers
                                  if (answer.isCorrect && currentRoomGameData?.current_question_data) {
                                    const questionId = currentRoomGameData.current_question_data.questionId;
                                    
                                    // Check if this is the first correct answer
                                    const isFirstCorrect = !firstCorrectAnswererByQuestion.has(questionId);
                                    
                                    if (isFirstCorrect) {
                                      console.log('[FOOTBALL_ANIMATION_DIRECT] First correct answer! Triggering animation immediately');
                                      
                                      // Mark as first correct answerer
                                      setFirstCorrectAnswererByQuestion(prev => new Map(prev).set(questionId, answer.userId));
                                      
                                      // Calculate position for scoring
                                      const correctAnswers = allAnswers.filter(a => 
                                        a.questionId === questionId && a.isCorrect
                                      );
                                      const position = correctAnswers.findIndex(a => a.userId === answer.userId) + 1;
                                      const bonusLevel = position === 1 ? 3 : position === 2 ? 2 : position === 3 ? 1 : 0;
                                      const scoreIncrease = position === 1 ? 25 : position === 2 ? 15 : 10;
                                      
                                      // Get card position from the event target directly
                                      const cardElement = e.currentTarget;
                                      const rect = cardElement.getBoundingClientRect();
                                      const animationPosition = {
                                        x: rect.left + rect.width / 2,
                                        y: rect.top + rect.height / 2 - 20 // Small offset to position just above card
                                      };
                                      
                                      // Create and trigger animation
                                      const uniqueAnimationId = `${answer.userId}-${questionId}-${Date.now()}`;
                                      const newAnimation = {
                                        id: uniqueAnimationId,
                                        type: 'enhanced' as const,
                                        trigger: Date.now(),
                                        scoreIncrease,
                                        bonusLevel,
                                        originPosition: animationPosition
                                      };
                                      
                                      console.log('[FOOTBALL_ANIMATION_DIRECT] Triggering animation immediately:', newAnimation);
                                      
                                      // Use flushSync to force immediate synchronous rendering
                                      flushSync(() => {
                                        setGlobalAnimations(prev => [...prev, newAnimation]);
                                      });
                                      
                                      // Clean up after animation duration (shorter than transition time)
                                      setTimeout(() => {
                                        setGlobalAnimations(prev => prev.filter(anim => anim.id !== uniqueAnimationId));
                                      }, 2500); // Reduced to 2.5s to ensure it completes before 3s transition
                                    }
                                  }
                                  
                                  // Still mark as landed for other purposes
                                  setLandedAnswers(prev => {
                                    const newSet = new Set(prev);
                                    newSet.add(answerKey);
                                    return newSet;
                                  });
                                }
                              }}
                            >
                              <span className={cn("truncate font-medium", answer.isOptimistic ? "text-white/80" : "text-white")}>
                                {playerName}
                                {answer.isOptimistic && <span className="ml-1 text-xs opacity-60">⏳</span>}
                              </span>

                            </div>
                          );
                        })
                      )}
                    </div>



                    <div className="mt-auto border-t border-slate-700 pt-2">
                      {(() => {
                        // Check if all players have submitted answers for the current round
                        // IMPORTANT: For game progression logic, we must only count CONFIRMED database answers
                        // Optimistic answers are for UI display only and don't trigger server progression
                        const realTimeAnswers: GameAnswer[] = Array.isArray(currentRoomGameData.current_round_answers)
                          ? currentRoomGameData.current_round_answers
                          : [];

                        const currentQuestionId = currentRoomGameData?.current_question_data?.questionId;
                        const answersForCurrentQuestion = realTimeAnswers.filter(answer =>
                          answer.questionId === currentQuestionId
                        );

                        const allPlayersSubmitted = answersForCurrentQuestion.length === playersInRoom.length;
                        const isHost = user && currentRoomGameData?.host_id === user.id;

                        // DIAGNOSTIC: Log the UI decision-making logic
                        console.log('[DIAGNOSTIC] UI Button Logic - Calculating whether to show Next Question button:', {
                          currentRoomGameData_current_round_answers: currentRoomGameData?.current_round_answers,
                          currentRoomGameData_current_round_answers_type: Array.isArray(currentRoomGameData?.current_round_answers) ? 'array' : typeof currentRoomGameData?.current_round_answers,
                          allAnswers,
                          allAnswersLength: allAnswers.length,
                          currentQuestionId,
                          answersForCurrentQuestion,
                          answersForCurrentQuestionLength: answersForCurrentQuestion.length,
                          playersInRoomLength: playersInRoom.length,
                          allPlayersSubmitted,
                          isHost,
                          willShowNextQuestionButton: allPlayersSubmitted && isHost,
                          currentRoomStatus: currentRoomGameData?.status,
                          hasOptimisticAnswer: allAnswers.some(a => a.isOptimistic)
                        });

                        return (
                          <>
                            {allPlayersSubmitted && isHost ? (
                              <>
                                <p className="text-xs text-green-400 text-center mb-2">All players submitted!</p>
                                <p className="text-xs text-gray-400 text-center mb-2">
                                  {currentRoomGameData?.transition_until 
                                    ? "Advancing to next question in 3 seconds..."
                                    : "Preparing next question..."}
                                </p>
                                {/* Add manual advance button as fallback */}
                                {currentRoomGameData?.transition_until && (
                                  <Button
                                    onClick={async () => {
                                      console.log('[MANUAL_ADVANCE] Host manually advancing to next question');
                                      // Force immediate transition by clearing transition_until
                                      const nextQuestion = currentRoomGameData.next_question_data;
                                      if (nextQuestion) {
                                        await supabase
                                          .from('game_rooms')
                                          .update({
                                            current_question_data: nextQuestion,
                                            current_round_answers: [],
                                            current_round_number: (currentRoomGameData.current_round_number || 1) + 1,
                                            question_started_at: new Date().toISOString(),
                                            transition_until: null,
                                            next_question_data: null
                                          })
                                          .eq('id', activeRoomId)
                                          .eq('status', 'active');
                                      }
                                    }}
                                    variant="outline"
                                    size="sm"
                                    className="mt-2 text-xs py-1 px-2"
                                  >
                                    Click if stuck →
                                  </Button>
                                )}
                              </>
                            ) : (
                              <div>
                                <p className="text-xs text-gray-500 text-center">
                                  {allPlayersSubmitted
                                    ? currentRoomGameData?.transition_until 
                                      ? "Next question coming soon..."
                                      : "Waiting for next question..."
                                    : `Game in Progress! (${answersForCurrentQuestion.length}/${playersInRoom.length} submitted)`
                                  }
                                </p>
                                {/* Add manual advance button for non-hosts if stuck */}
                                {allPlayersSubmitted && currentRoomGameData?.transition_until && (
                                  <Button
                                    onClick={async () => {
                                      console.log('[MANUAL_ADVANCE] Non-host manually triggering transition check');
                                      // Force a state sync to pick up any missed transitions
                                      await syncFullRoomState(activeRoomId, 'manual_advance');
                                    }}
                                    variant="ghost"
                                    size="sm"
                                    className="mt-2 text-xs py-1 px-2 w-full opacity-50 hover:opacity-100"
                                  >
                                    Stuck? Click to refresh
                                  </Button>
                                )}
                              </div>
                            )}
                            <Button
                              onClick={handleLeaveRoom}
                              className="w-full mt-3 bg-red-600 hover:bg-red-700"
                            >
                              Leave Game
                            </Button>
                          </>
                        );
                      })()}
                    </div>
                  </>
                ) : (
                  // Fallback for 'finished' or other states
                  <div className="flex flex-col h-full">
                    <div className="flex-1 flex items-center justify-center">
                      <p className="text-gray-400">
                        {currentRoomGameData?.status === 'finished' ? 'Game Finished!' : 'Loading room state...'}
                      </p>
                    </div>
                    <div className="mt-auto border-t border-slate-700 pt-2">
                      <Button
                        onClick={handleLeaveRoom}
                        className="w-full mt-3 bg-red-600 hover:bg-red-700"
                      >
                        Leave Game
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        </div>
      </div>

      {/* Global Animations - rendered through portals at root level */}
      {hasMounted && (
        <>
          {globalAnimations.length > 0 && console.log('[FOOTBALL_ANIMATION_RENDER] Rendering animations:', {
            count: globalAnimations.length,
            animations: globalAnimations
          })}
          {globalAnimations.map(animation => {
            if (animation.type === 'enhanced') {
              console.log('[FOOTBALL_ANIMATION_RENDER] Rendering GlobalMultiplayerScoreAnimation:', animation);
              return (
                <GlobalMultiplayerScoreAnimation
                  key={animation.id}
                  trigger={animation.trigger}
                  scoreIncrease={animation.scoreIncrease || 10}
                  bonusLevel={animation.bonusLevel || 0}
                  originPosition={animation.originPosition}
                />
              );
            } else {
              return (
                <GlobalFallbackAnimation
                  key={animation.id}
                  animationKey={animation.id}
                  originPosition={animation.originPosition}
                />
              );
            }
          })}
        </>
      )}
    </main>
  );
}

// Complete client-side wrapper to prevent all SSR issues

function ClientOnlyWrapper() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) {
    return (
      <div className="flex min-h-screen items-center justify-center text-white bg-gray-900">
        <div>Loading...</div>
      </div>
    );
  }
  
  return <HomePageContent />;
}

const DynamicWrapper = dynamic(() => Promise.resolve(ClientOnlyWrapper), {
  ssr: false,
  loading: () => (
    <div className="flex min-h-screen items-center justify-center text-white bg-gray-900">
      <div>Loading...</div>
    </div>
  )
});

export default function HomePage() {
  return <DynamicWrapper />;
}
