# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Development
```bash
# Start development server (from root)
npm run dev

# Start development server (from web-app)
cd web-app && npm run dev
```

### Build and Production
```bash
# Build for production
npm run build

# Build without CSS verification (if CSS verification fails)
cd web-app && npm run build:safe

# Start production server
npm start
```

### Linting and Code Quality
```bash
# Run linting
npm run lint

# Run ESLint specifically on src directory
cd web-app && npm run lint:eslint
```

### CSS Management
```bash
# Verify CSS output after build
cd web-app && npm run verify-css

# Backup current CSS (PowerShell)
cd web-app && npm run css:backup

# Restore CSS from backup (PowerShell)
cd web-app && npm run css:restore
```

### Supabase Edge Functions Deployment
```powershell
# Deploy using PowerShell scripts (when standard CLI fails)
cd supabase
.\deploy-leave-handler.ps1
.\deploy-start-game-handler.ps1
.\deploy-submit-answer-handler.ps1
.\deploy-stale-room-janitor.ps1
.\deploy-player-disconnect-janitor.ps1
```

## Project Architecture

### Overview
Recognition Combine is a full-stack sports trivia game featuring NFL player recognition. Built with Next.js 15 (App Router) and Supabase, supporting both single-player and real-time multiplayer modes.

### Technology Stack
- **Frontend**: Next.js 15.3.3, React 19, TypeScript, Tailwind CSS
- **State Management**: Zustand
- **Backend**: Supabase (PostgreSQL + Realtime + Edge Functions)
- **Deployment**: Vercel (frontend), Supabase (backend)

### Key Directories

#### `/web-app/src/app` - Next.js App Router
- `page.tsx` - Main game component (2500+ lines) handling all game modes
- Single route application - all game modes on main page
- Uses App Router patterns with server/client components

#### `/web-app/src/stores` - Zustand State Management
- `gameStore.ts` - Central game state:
  - Game states: idle, countdown, playing, finished
  - Score tracking, streaks, timer management
  - Question generation and answer validation

#### `/web-app/src/components` - React Components
- `/auth` - Authentication modal
- `/game` - Game-specific components (PlayerImageDisplay, ChoiceButton, ScorePanel)
- `/ui` - Reusable UI components (shadcn/ui pattern)

#### `/supabase/functions` - Edge Functions (Deno)
- `start-game-handler` - Initialize multiplayer games
- `submit-answer-handler` - Process answer submissions  
- `leave-room-handler` - Handle player exits
- `stale-room-janitor` - Cleanup abandoned rooms
- Each function includes CORS handling and error management

### Database Schema

#### Core Tables
1. **`profiles`** - User profiles (linked to auth.users)
2. **`game_rooms`** - Multiplayer game sessions
3. **`game_players`** - Players in rooms with connection tracking
4. **`player_game_stats`** - Historical statistics

#### Key Features
- Row Level Security (RLS) on all tables
- Realtime subscriptions for multiplayer
- JSONB fields for flexible data storage

### Multiplayer Architecture

#### Three-Layer Connection System
1. Supabase authentication state
2. Realtime channel subscriptions  
3. Application-level connection tracking

#### Real-time Features
- Player join/leave notifications
- Live score updates during games
- Synchronized game state across clients
- Automatic reconnection handling

### Critical CSS Protection Rules

**⚠️ NEVER MODIFY WITHOUT BACKUP - CSS IS WORKING**

1. **Global CSS Import** - MUST exist in `web-app/src/app/layout.tsx` line 2:
   ```typescript
   import "./globals.css";
   ```

2. **Tailwind Content Paths** - Must include all component directories
3. **Never change** CSS import order or remove OKLCH colors
4. **Protected files**: globals.css, tailwind.config.ts, postcss configs

### PowerShell Environment Notes
- Use PowerShell syntax (no `&&` operators)
- Scripts use line-separated commands
- Deployment scripts provided for Edge Functions
- Full paths: `C:\Projects\recognition-combine`

### Environment Files
- `web-app/.env.local` - EXISTS (hidden for security)
- `supabase/.env` - EXISTS (hidden for security)

### Development Best Practices
1. Always verify database schema before implementing features
2. Test with both single-player and multiplayer modes
3. Handle race conditions in authentication flows
4. Maintain CSS protection rules strictly
5. Use provided PowerShell scripts for deployments
6. Check for existing state variables before adding new ones
7. Follow existing code patterns and conventions

### Common Issues and Solutions
1. **CSS Missing**: Check layout.tsx import, run CSS restore
2. **Edge Function Deploy Fails**: Use PowerShell scripts
3. **Auth Race Conditions**: Check tab focus handling
4. **Realtime Disconnects**: Verify three-layer connection system