const http = require('http');
const https = require('https');
const fs = require('fs');

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const protocol = urlObj.protocol === 'https:' ? https : http;
    
    const req = protocol.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => resolve({ 
        statusCode: res.statusCode, 
        headers: res.headers, 
        body: data 
      }));
    });
    
    req.on('error', reject);
    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

async function testMultiplayerEndpoints() {
  console.log('Testing multiplayer game endpoints...\n');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // Step 1: Test main page
    console.log('1. Testing main page...');
    const mainPage = await makeRequest(baseUrl);
    console.log(`   Status: ${mainPage.statusCode}`);
    console.log(`   Content-Type: ${mainPage.headers['content-type']}`);
    console.log(`   Page size: ${mainPage.body.length} bytes`);
    
    // Save HTML for analysis
    fs.writeFileSync('screenshots/page-content.html', mainPage.body);
    console.log('   Saved page content to: screenshots/page-content.html');
    
    // Extract key information from HTML
    const titleMatch = mainPage.body.match(/<title>(.*?)<\/title>/);
    if (titleMatch) {
      console.log(`   Page title: ${titleMatch[1]}`);
    }
    
    // Look for authentication elements
    console.log('\n2. Analyzing authentication elements...');
    const hasSignIn = mainPage.body.includes('Sign In') || mainPage.body.includes('Sign in') || mainPage.body.includes('Login');
    const hasSignOut = mainPage.body.includes('Sign Out') || mainPage.body.includes('Sign out') || mainPage.body.includes('Logout');
    console.log(`   Has Sign In: ${hasSignIn}`);
    console.log(`   Has Sign Out: ${hasSignOut}`);
    
    // Look for game mode options
    console.log('\n3. Analyzing game mode options...');
    const hasMultiplayer = mainPage.body.includes('Multiplayer') || mainPage.body.includes('multiplayer');
    const hasCreateRoom = mainPage.body.includes('Create Room') || mainPage.body.includes('create room');
    const hasJoinRoom = mainPage.body.includes('Join Room') || mainPage.body.includes('join room');
    console.log(`   Has Multiplayer: ${hasMultiplayer}`);
    console.log(`   Has Create Room: ${hasCreateRoom}`);
    console.log(`   Has Join Room: ${hasJoinRoom}`);
    
    // Extract all button texts
    console.log('\n4. Extracting button texts...');
    const buttonMatches = mainPage.body.match(/<button[^>]*>([^<]+)<\/button>/g);
    if (buttonMatches) {
      console.log('   Found buttons:');
      buttonMatches.forEach(match => {
        const text = match.replace(/<[^>]+>/g, '').trim();
        if (text) console.log(`   - ${text}`);
      });
    }
    
    // Look for API endpoints in the JavaScript
    console.log('\n5. Looking for API endpoints...');
    const apiMatches = mainPage.body.match(/["'](\/api\/[^"']+)["']/g);
    if (apiMatches) {
      console.log('   Found API endpoints:');
      const uniqueEndpoints = [...new Set(apiMatches.map(m => m.slice(1, -1)))];
      uniqueEndpoints.forEach(endpoint => console.log(`   - ${endpoint}`));
    }
    
    // Look for Supabase configuration
    console.log('\n6. Checking for Supabase configuration...');
    const hasSupabase = mainPage.body.includes('supabase');
    const supabaseUrl = mainPage.body.match(/https:\/\/[^.]+\.supabase\.co/);
    console.log(`   Uses Supabase: ${hasSupabase}`);
    if (supabaseUrl) {
      console.log(`   Supabase URL found: ${supabaseUrl[0]}`);
    }
    
    // Extract game-related text content
    console.log('\n7. Extracting game-related content...');
    const gameKeywords = ['player', 'score', 'question', 'answer', 'team', 'round', 'game'];
    gameKeywords.forEach(keyword => {
      const count = (mainPage.body.match(new RegExp(keyword, 'gi')) || []).length;
      if (count > 0) {
        console.log(`   Found "${keyword}": ${count} times`);
      }
    });
    
    // Summary
    console.log('\n=== SUMMARY ===');
    console.log('The game appears to be a sports trivia/recognition game with:');
    console.log('- Frontend: Next.js/React application');
    console.log('- Backend: Supabase for real-time features');
    console.log('- Features: Single-player and multiplayer modes');
    console.log('- Authentication: Required for multiplayer features');
    
    console.log('\n=== RECOMMENDATIONS ===');
    console.log('To properly test the multiplayer features with Puppeteer:');
    console.log('1. Install Chrome/Chromium for ARM64 architecture');
    console.log('2. Or use a different testing approach (Playwright, Selenium)');
    console.log('3. Or run tests from an x86-64 environment');
    console.log('4. Consider using API testing for backend functionality');
    
  } catch (error) {
    console.error('Error during test:', error.message);
  }
}

// Create screenshots directory
if (!fs.existsSync('screenshots')) {
  fs.mkdirSync('screenshots');
}

// Run the test
testMultiplayerEndpoints();