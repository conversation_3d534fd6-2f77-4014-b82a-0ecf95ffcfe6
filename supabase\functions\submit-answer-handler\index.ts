// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

// Type definitions
interface GameAnswer {
  userId: string;
  questionId: string;
  choiceName: string;
  timestamp: number;
  isCorrect: boolean;
}

interface PlayerChoice {
  name: string;
  isCorrect: boolean;
}

interface PlayerQuestion {
  questionId: string;
  correctPlayerId: number;
  imageUrl: string;
  choices: PlayerChoice[];
  correctChoiceName: string;
}

interface SubmitAnswerRequestBody {
  roomId: string;
  choiceName: string;
  questionId?: string; // Optional for backward compatibility
}

interface PlayerData {
  id: number;
  team_name: string;
  player_name: string;
  local_image_path: string | null;
  jersey_number: string | number | null;
  position: string | null;
  height: string | null;
  weight: string | number | null;
  age_or_dob: string | number | null;
  experience: string | number | null;
  college: string | null;
}

// Constants
const SIMULTANEOUS_WINDOW_MS = 100; // 100ms window for tie detection

// Helper to shuffle array (Fisher-Yates algorithm)
function shuffleArray<T>(array: T[]): T[] {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]]; // Swap elements
  }
  return array;
}

// Load player data from the JSON file
async function loadPlayerData(): Promise<PlayerData[]> {
  try {
    // For now, use a hardcoded sample of players since we can't easily access the static file from edge function
    // In a production setup, this data could be stored in Supabase database or storage
    console.log(`[EDGE_SUBMIT_ANSWER] Using hardcoded player data sample`);
    return [
      { id: 1, team_name: 'Arizona Cardinals', player_name: 'Isaiah Adams', local_image_path: 'arizona-cardinals/isaiah-adams.jpg', jersey_number: '74', position: 'OL', height: '6-4', weight: '315', age_or_dob: '24', experience: '2', college: 'Illinois' },
      { id: 2, team_name: 'Arizona Cardinals', player_name: 'Andre Baccellia', local_image_path: 'arizona-cardinals/andre-baccellia.jpg', jersey_number: '82', position: 'WR', height: '5-10', weight: '175', age_or_dob: '28', experience: '2', college: 'Washington' },
      { id: 3, team_name: 'Arizona Cardinals', player_name: 'Budda Baker', local_image_path: 'arizona-cardinals/budda-baker.jpg', jersey_number: '3', position: 'S', height: '5-10', weight: '195', age_or_dob: '29', experience: '9', college: 'Washington' },
      { id: 4, team_name: 'Arizona Cardinals', player_name: 'Kelvin Beachum', local_image_path: 'arizona-cardinals/kelvin-beachum.jpg', jersey_number: '68', position: 'OL', height: '6-3', weight: '308', age_or_dob: '35', experience: '14', college: 'Southern Methodist' },
      { id: 5, team_name: 'Arizona Cardinals', player_name: 'Trey Benson', local_image_path: 'arizona-cardinals/trey-benson.jpg', jersey_number: '33', position: 'RB', height: '6-0', weight: '216', age_or_dob: '22', experience: '2', college: 'Florida State' },
      { id: 6, team_name: 'Arizona Cardinals', player_name: 'Joey Blount', local_image_path: 'arizona-cardinals/joey-blount.jpg', jersey_number: '32', position: 'S', height: '6-1', weight: '201', age_or_dob: '26', experience: '4', college: 'Virginia' },
      { id: 7, team_name: 'Arizona Cardinals', player_name: 'Ekow Boye-Doe', local_image_path: 'arizona-cardinals/ekow-boye-doe.jpg', jersey_number: '35', position: 'CB', height: '6-0', weight: '177', age_or_dob: '25', experience: '2', college: 'Kansas State' },
      { id: 8, team_name: 'Arizona Cardinals', player_name: 'Aaron Brewer', local_image_path: 'arizona-cardinals/aaron-brewer.jpg', jersey_number: '46', position: 'LS', height: '6-5', weight: '232', age_or_dob: '34', experience: '14', college: 'San Diego State' },
      { id: 9, team_name: 'Arizona Cardinals', player_name: 'Jacoby Brissett', local_image_path: 'arizona-cardinals/jacoby-brissett.jpg', jersey_number: '7', position: 'QB', height: '6-4', weight: '235', age_or_dob: '32', experience: '10', college: 'N.C. State' },
      { id: 11, team_name: 'Arizona Cardinals', player_name: 'Evan Brown', local_image_path: 'arizona-cardinals/evan-brown.jpg', jersey_number: '63', position: 'OL', height: '6-3', weight: '320', age_or_dob: '28', experience: '7', college: 'SMU' },
      { id: 12, team_name: 'Arizona Cardinals', player_name: 'Baron Browning', local_image_path: 'arizona-cardinals/baron-browning.jpg', jersey_number: '5', position: 'OLB', height: '6-3', weight: '240', age_or_dob: '26', experience: '5', college: 'Ohio State' },
      { id: 16, team_name: 'Arizona Cardinals', player_name: 'Calais Campbell', local_image_path: 'arizona-cardinals/calais-campbell.jpg', jersey_number: '93', position: 'DL', height: '6-8', weight: '307', age_or_dob: '38', experience: '18', college: 'Miami' },
      { id: 18, team_name: 'Arizona Cardinals', player_name: 'Michael Carter', local_image_path: 'arizona-cardinals/michael-carter.jpg', jersey_number: '22', position: 'RB', height: '5-8', weight: '201', age_or_dob: '26', experience: '4', college: 'North Carolina' },
      { id: 19, team_name: 'Arizona Cardinals', player_name: 'Kei\'Trel Clark', local_image_path: 'arizona-cardinals/keitrel-clark.jpg', jersey_number: '13', position: 'CB', height: '5-10', weight: '181', age_or_dob: '24', experience: '3', college: 'Louisville' },
      { id: 20, team_name: 'Arizona Cardinals', player_name: 'L.J. Collier', local_image_path: 'arizona-cardinals/l.j.-collier.jpg', jersey_number: '91', position: 'DL', height: '6-2', weight: '291', age_or_dob: '29', experience: '7', college: 'TCU' },
      { id: 21, team_name: 'Arizona Cardinals', player_name: 'Zaven Collins', local_image_path: 'arizona-cardinals/zaven-collins.jpg', jersey_number: '25', position: 'OLB', height: '6-4', weight: '260', age_or_dob: '25', experience: '5', college: 'Tulsa' },
      { id: 23, team_name: 'Arizona Cardinals', player_name: 'James Conner', local_image_path: 'arizona-cardinals/james-conner.jpg', jersey_number: '6', position: 'RB', height: '6-1', weight: '233', age_or_dob: '30', experience: '9', college: 'Pittsburgh' },
      { id: 24, team_name: 'Arizona Cardinals', player_name: 'Kitan Crawford', local_image_path: 'arizona-cardinals/kitan-crawford.jpg', jersey_number: '36', position: 'S', height: '5-11', weight: '202', age_or_dob: '23', experience: 'R', college: 'Nevada' },
      { id: 25, team_name: 'Arizona Cardinals', player_name: 'Jake Curhan', local_image_path: 'arizona-cardinals/jake-curhan.jpg', jersey_number: '64', position: 'OL', height: '6-6', weight: '316', age_or_dob: '27', experience: '5', college: 'California' },
      { id: 26, team_name: 'Arizona Cardinals', player_name: 'McClendon Curtis', local_image_path: 'arizona-cardinals/mcclendon-curtis.jpg', jersey_number: '66', position: 'OL', height: '6-6', weight: '325', age_or_dob: '25', experience: '2', college: 'Tennessee-Chattanooga' }
    ];
  } catch (error) {
    console.error('[EDGE_SUBMIT_ANSWER] Error loading player data:', error);
    // Fallback to minimal sample
    return [
      { id: 1, team_name: 'Arizona Cardinals', player_name: 'Isaiah Adams', local_image_path: 'arizona-cardinals/isaiah-adams.jpg', jersey_number: '74', position: 'OL', height: '6-4', weight: '315', age_or_dob: '24', experience: '2', college: 'Illinois' },
      { id: 2, team_name: 'Arizona Cardinals', player_name: 'Andre Baccellia', local_image_path: 'arizona-cardinals/andre-baccellia.jpg', jersey_number: '82', position: 'WR', height: '5-10', weight: '175', age_or_dob: '28', experience: '2', college: 'Washington' },
      { id: 3, team_name: 'Arizona Cardinals', player_name: 'Budda Baker', local_image_path: 'arizona-cardinals/budda-baker.jpg', jersey_number: '3', position: 'S', height: '5-10', weight: '195', age_or_dob: '29', experience: '9', college: 'Washington' },
      { id: 4, team_name: 'Arizona Cardinals', player_name: 'Kelvin Beachum', local_image_path: 'arizona-cardinals/kelvin-beachum.jpg', jersey_number: '68', position: 'OL', height: '6-3', weight: '308', age_or_dob: '35', experience: '14', college: 'Southern Methodist' }
    ];
  }
}

// Generate a question using the same logic as next-question-handler
function generateQuestion(allPlayers: PlayerData[], excludeIds: Set<number> = new Set()): PlayerQuestion | null {
  if (!allPlayers || allPlayers.length < 4) {
    console.error("[EDGE_SUBMIT_ANSWER] Not enough player data loaded or available to generate a question.");
    return null;
  }

  // Filter out players already asked
  const availablePlayers = allPlayers.filter(
    (p) => p.id != null && !excludeIds.has(p.id)
  );

  if (availablePlayers.length < 1) {
    console.warn("[EDGE_SUBMIT_ANSWER] No more available players to ask questions about.");
    return null;
  }

  // Select the correct player
  const correctPlayerIndex = Math.floor(Math.random() * availablePlayers.length);
  const correctPlayer = availablePlayers[correctPlayerIndex];

  // Select 3 distractors (ensure they are different from correct player and each other)
  const distractors: PlayerData[] = [];
  const potentialDistractors = allPlayers.filter(p => p.id !== correctPlayer.id);
  shuffleArray(potentialDistractors); // Shuffle to get random distractors

  for (const p of potentialDistractors) {
    if (distractors.length < 3) {
      distractors.push(p);
    } else {
      break;
    }
  }

  if (distractors.length < 3) {
    console.error("[EDGE_SUBMIT_ANSWER] Could not find enough unique distractors.");
    return null;
  }

  // Create choices array
  const choices: PlayerChoice[] = [
    { name: correctPlayer.player_name, isCorrect: true },
    ...distractors.map(p => ({ name: p.player_name, isCorrect: false }))
  ];

  // Shuffle choices
  const shuffledChoices = shuffleArray(choices);

  // Construct the full image URL
  const imageUrl = correctPlayer.local_image_path
    ? `/players_images/${correctPlayer.local_image_path}`
    : '/images/placeholder.jpg';

  return {
    questionId: crypto.randomUUID(),
    correctPlayerId: correctPlayer.id,
    imageUrl: imageUrl,
    choices: shuffledChoices,
    correctChoiceName: correctPlayer.player_name
  };
}

console.log('[EDGE_FN_LOAD] submit-answer-handler function script loaded.');

serve(async (req: Request) => {
  console.log(`[EDGE_SUBMIT_ANSWER] Request received. Method: ${req.method}, URL: ${req.url}`);
  
  if (req.method === 'OPTIONS') {
    console.log('[EDGE_SUBMIT_ANSWER] OPTIONS request, responding with CORS.');
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[EDGE_SUBMIT_ANSWER] Processing POST request for submit-answer-handler');
    
    // Environment variable checks
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceRoleKey) {
      console.error('[EDGE_SUBMIT_ANSWER] CRITICAL: Missing environment variables');
      throw new Error('Server configuration error: Missing Supabase credentials.')
    }
    console.log('[EDGE_SUBMIT_ANSWER] Environment variables loaded successfully');
    
    // Parse request body
    console.log('[EDGE_SUBMIT_ANSWER] Parsing request body...');
    const requestBody: SubmitAnswerRequestBody = await req.json()
    console.log('[EDGE_SUBMIT_ANSWER] Parsed request body:', requestBody);
    const { roomId, choiceName, questionId } = requestBody;

    if (!roomId || !choiceName) {
      console.error('[EDGE_SUBMIT_ANSWER] Missing required fields in request body');
      throw new Error('Room ID and choice name are required.')
    }
    console.log(`[EDGE_SUBMIT_ANSWER] Processing answer submission for room ${roomId}, choice: ${choiceName}`);
    
    // Authentication
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('[EDGE_SUBMIT_ANSWER] Missing Authorization header');
      throw new Error('User not authenticated: Missing Authorization header.')
    }
    console.log('[EDGE_SUBMIT_ANSWER] Authorization header present, verifying user...');

    const userClient = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } },
    })
    console.log('[EDGE_SUBMIT_ANSWER] User client created, getting user...');
    
    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError) {
      console.error('[EDGE_SUBMIT_ANSWER] User authentication error:', userError);
      throw new Error('User not authenticated: ' + userError.message)
    }
    if (!user) {
      console.error('[EDGE_SUBMIT_ANSWER] No user found in session');
      throw new Error('User not authenticated or not found.')
    }
    console.log(`[EDGE_SUBMIT_ANSWER] User authenticated successfully. User ID: ${user.id}`);
    const userId = user.id

    // Create admin client
    console.log('[EDGE_SUBMIT_ANSWER] Creating admin client...');
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)
    console.log('[EDGE_SUBMIT_ANSWER] Admin client created successfully');

    console.log(`[EDGE_SUBMIT_ANSWER] Processing answer submission for room ${roomId} by user ${userId}`)

    // Fetch current room state and validate
    console.log(`[EDGE_SUBMIT_ANSWER] Fetching current room state for validation...`);
    const { data: roomData, error: roomFetchError } = await supabaseAdmin
      .from('game_rooms')
      .select('*, game_players(user_id, is_ready)')
      .eq('id', roomId)
      .single()

    if (roomFetchError) {
      console.error(`[EDGE_SUBMIT_ANSWER] Database error fetching room ${roomId}:`, roomFetchError);
      throw new Error('Database error: ' + roomFetchError.message)
    }
    if (!roomData) {
      console.error(`[EDGE_SUBMIT_ANSWER] Room ${roomId} not found in database`);
      throw new Error('Room not found.')
    }
    console.log(`[EDGE_SUBMIT_ANSWER] Room state fetched:`, {
      id: roomData.id,
      status: roomData.status,
      current_round_number: roomData.current_round_number,
      playersCount: roomData.game_players?.length || 0
    });

    // Validate game is active
    if (roomData.status !== 'active') {
      console.error(`[EDGE_SUBMIT_ANSWER] Game is not active. Status: ${roomData.status}`);
      return new Response(JSON.stringify({ 
        error: 'Game is not active.',
        details: `Game status is '${roomData.status}', expected 'active'`,
        currentStatus: roomData.status
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Validate user is in the game
    const playersInRoom = roomData.game_players || [];
    const userInGame = playersInRoom.find((p: any) => p.user_id === userId);
    if (!userInGame) {
      console.error(`[EDGE_SUBMIT_ANSWER] User ${userId} is not in room ${roomId}`);
      return new Response(JSON.stringify({ 
        error: 'User not in game.',
        details: `User ${userId} is not a player in room ${roomId}`
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 403,
      });
    }

    // Validate current question exists
    const currentQuestion: PlayerQuestion = roomData.current_question_data;
    if (!currentQuestion) {
      console.error(`[EDGE_SUBMIT_ANSWER] No current question in room ${roomId}`);
      return new Response(JSON.stringify({ 
        error: 'No current question.',
        details: 'No question is currently active in this game'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Validate question ID matches if provided (for sync validation)
    if (questionId && questionId !== currentQuestion.questionId) {
      console.error(`[EDGE_SUBMIT_ANSWER] Question ID mismatch. Client: ${questionId}, Server: ${currentQuestion.questionId}`);
      return new Response(JSON.stringify({ 
        error: 'Question mismatch.',
        details: 'Your client is on a different question than the server',
        clientQuestionId: questionId,
        serverQuestionId: currentQuestion.questionId
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 409, // Conflict status to indicate sync issue
      });
    }

    // Validate choice exists in current question
    const validChoice = currentQuestion.choices.find(choice => choice.name === choiceName);
    if (!validChoice) {
      console.error(`[EDGE_SUBMIT_ANSWER] Invalid choice '${choiceName}' for question ${currentQuestion.questionId}`);
      return new Response(JSON.stringify({ 
        error: 'Invalid choice.',
        details: `Choice '${choiceName}' is not valid for the current question`,
        validChoices: currentQuestion.choices.map(c => c.name)
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    console.log(`[EDGE_SUBMIT_ANSWER] Validation passed. Processing answer: ${choiceName} (correct: ${validChoice.isCorrect})`);

    // Check if user has already answered this question
    const currentAnswers: GameAnswer[] = Array.isArray(roomData.current_round_answers) ? roomData.current_round_answers : [];
    const existingAnswer = currentAnswers.find(answer => answer.userId === userId && answer.questionId === currentQuestion.questionId);
    
    if (existingAnswer) {
      console.warn(`[EDGE_SUBMIT_ANSWER] User ${userId} has already answered question ${currentQuestion.questionId}`);
      return new Response(JSON.stringify({ 
        error: 'Already answered.',
        details: 'You have already submitted an answer for this question',
        existingAnswer: existingAnswer.choiceName
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 409,
      });
    }

    // Create new answer with current timestamp
    const now = Date.now();
    const newAnswer: GameAnswer = {
      userId: userId,
      questionId: currentQuestion.questionId,
      choiceName: choiceName,
      timestamp: now,
      isCorrect: validChoice.isCorrect
    };

    console.log(`[EDGE_SUBMIT_ANSWER] Creating new answer:`, newAnswer);

    // Add the new answer to the current round answers
    const updatedAnswers = [...currentAnswers, newAnswer];

    // Update player scores and bonus levels if answer is correct
    const currentScores: Record<string, number> = roomData.player_scores || {};
    const currentBonusLevels: Record<string, number> = roomData.player_bonus_levels || {};
    let updatedScores = { ...currentScores };
    let updatedBonusLevels = { ...currentBonusLevels };
    let scoreIncrease = 0;

    if (validChoice.isCorrect) {
      // Base score: +10 for correct answer
      scoreIncrease = 10;

      // Get current bonus level for this player (0 = no streak, 1 = BQ1, 2 = BQ2, 3+ = BQ3)
      const currentBonusLevel = currentBonusLevels[userId] || 0;
      const newBonusLevel = currentBonusLevel + 1;

      // Calculate bonus based on consecutive correct answers (BQ system)
      let bonus = 0;
      if (newBonusLevel === 1) {
        // BQ1: +5 bonus
        bonus = 5;
      } else if (newBonusLevel === 2) {
        // BQ2: +5+5 bonus (stacking)
        bonus = 10; // 5 + 5
      } else if (newBonusLevel >= 3) {
        // BQ3+: +5+10 bonus (stacking)
        bonus = 15; // 5 + 10
      }

      const totalScoreIncrease = scoreIncrease + bonus;
      updatedScores[userId] = (currentScores[userId] || 0) + totalScoreIncrease;
      updatedBonusLevels[userId] = newBonusLevel;

      console.log(`[EDGE_SUBMIT_ANSWER] Score update for user ${userId}:`, {
        baseScore: scoreIncrease,
        bonus: bonus,
        totalIncrease: totalScoreIncrease,
        newTotal: updatedScores[userId],
        bonusLevel: newBonusLevel
      });
    } else {
      // Reset bonus level on incorrect answer
      updatedBonusLevels[userId] = 0;
      console.log(`[EDGE_SUBMIT_ANSWER] Incorrect answer for user ${userId}, bonus level reset to 0`);
    }

    // CRITICAL FIX: Check if this is the final answer and advance game if so
    const allPlayersHaveAnswered = updatedAnswers.length === playersInRoom.length;
    console.log(`[EDGE_SUBMIT_ANSWER] Answer count check: ${updatedAnswers.length}/${playersInRoom.length} players answered. Final answer: ${allPlayersHaveAnswered}`);

    if (allPlayersHaveAnswered) {
      // All players have answered - set transition deadline for 3 seconds from now
      console.log('[EDGE_SUBMIT_ANSWER] This is the final answer. Setting 3-second transition deadline...');

      // Calculate transition deadline (3 seconds from now)
      const currentDeadline = roomData.transition_deadline ? new Date(roomData.transition_deadline).getTime() : Infinity;
      const newDeadline = Date.now() + 3000;
      const transitionDeadline = new Date(Math.min(currentDeadline, newDeadline)).toISOString();
      
      // Update the room with transition deadline
      console.log(`[EDGE_SUBMIT_ANSWER] Setting transition deadline to ${transitionDeadline}`);
      const { data: transitionUpdate, error: transitionError } = await supabaseAdmin
        .from('game_rooms')
        .update({
          current_round_answers: updatedAnswers,
          player_scores: updatedScores,
          player_bonus_levels: updatedBonusLevels,
          transition_deadline: transitionDeadline,
          last_activity_timestamp: new Date().toISOString()
        })
        .eq('id', roomId)
        .eq('status', 'active')
        .select('id, transition_deadline')

      if (transitionError) {
        console.error(`[EDGE_SUBMIT_ANSWER] Database error setting transition deadline:`, transitionError);
        throw new Error(`Failed to set transition deadline: ${transitionError.message}`)
      }

      console.log(`[EDGE_SUBMIT_ANSWER] Transition deadline set. Server monitor will advance game at ${transitionDeadline}`);

      // Return success response indicating we're in transition
      return new Response(JSON.stringify({ 
        message: 'Final answer submitted! Showing results for 3 seconds...',
        answer: newAnswer,
        isCorrect: validChoice.isCorrect,
        newScore: updatedScores[userId] || 0,
        totalAnswers: updatedAnswers.length,
        inTransition: true,
        transitionDeadline,
        timestamp: new Date().toISOString()
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })

    } else {
      // This is NOT the final answer - just update the answers and scores
      console.log(`[EDGE_SUBMIT_ANSWER] Answer submitted, but waiting for more players. ${updatedAnswers.length}/${playersInRoom.length} submitted.`);

      // CRITICAL: If this is the FIRST answer and no deadline exists, set the 7-second hard cap
      let updateData: any = {
        current_round_answers: updatedAnswers,
        player_scores: updatedScores,
        player_bonus_levels: updatedBonusLevels,
        last_activity_timestamp: new Date().toISOString()
      };

      if (updatedAnswers.length === 1 && !roomData.transition_deadline) {
        const questionStartTime = roomData.question_started_at ? new Date(roomData.question_started_at).getTime() : now;
        const hardCapDeadline = new Date(questionStartTime + 7000).toISOString();
        updateData.transition_deadline = hardCapDeadline;
        console.log(`[EDGE_SUBMIT_ANSWER] First answer - setting 7-second hard cap deadline: ${hardCapDeadline}`);
      }

      // Update the room with new answer, scores, and bonus levels (standard flow)
      console.log(`[EDGE_SUBMIT_ANSWER] Updating room with new answer, scores, and bonus levels...`);
      const { data: updateResult, error: updateError } = await supabaseAdmin
        .from('game_rooms')
        .update(updateData)
        .eq('id', roomId)
        .eq('status', 'active') // Ensure game is still active
        .select('id, current_round_answers, player_scores, player_bonus_levels, transition_deadline')

      if (updateError) {
        console.error(`[EDGE_SUBMIT_ANSWER] Database error updating room ${roomId}:`, updateError);
        throw new Error(`Failed to submit answer - database update error: ${updateError.message}`)
      }

      if (!updateResult || updateResult.length === 0) {
        console.error(`[EDGE_SUBMIT_ANSWER] Room ${roomId} was not updated - game may have ended`);
        return new Response(JSON.stringify({ 
          error: 'Game state changed.',
          details: 'The game state changed while processing your answer. Please try again.',
          conflictType: 'GAME_STATE_CHANGED'
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 409,
        });
      }

      console.log(`[EDGE_SUBMIT_ANSWER] Answer submitted successfully for user ${userId} in room ${roomId}`);
      console.log(`[EDGE_SUBMIT_ANSWER] Answer details:`, {
        choiceName,
        isCorrect: validChoice.isCorrect,
        newScore: updatedScores[userId] || 0,
        totalAnswers: updatedAnswers.length
      });

      // Return success response (standard flow)
      return new Response(JSON.stringify({ 
        message: 'Answer submitted successfully!',
        answer: newAnswer,
        isCorrect: validChoice.isCorrect,
        newScore: updatedScores[userId] || 0,
        totalAnswers: updatedAnswers.length,
        gameAdvanced: false,
        timestamp: new Date().toISOString()
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }

  } catch (error) {
    console.error('[EDGE_SUBMIT_ANSWER] UNHANDLED EXCEPTION in submit-answer-handler:', error);
    if (error instanceof Error) {
      console.error('[EDGE_SUBMIT_ANSWER] Exception Name:', error.name);
      console.error('[EDGE_SUBMIT_ANSWER] Exception Message:', error.message);
      console.error('[EDGE_SUBMIT_ANSWER] Exception Stack:', error.stack);
    }
    
    // Determine appropriate status code based on error type/message
    let statusCode = 500; // Default to internal server error
    let errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    if (errorMessage.includes('Room ID') || errorMessage.includes('choice name') || errorMessage.includes('required')) {
      statusCode = 400; // Bad Request for missing required fields
    } else if (errorMessage.includes('not authenticated') || errorMessage.includes('Missing Authorization')) {
      statusCode = 401; // Unauthorized for authentication issues
    } else if (errorMessage.includes('Room not found')) {
      statusCode = 404; // Not Found for missing resources
    } else if (errorMessage.includes('Database error')) {
      statusCode = 500; // Keep as 500 for actual database issues
      errorMessage = 'Database error occurred while processing request';
    } else if (errorMessage.includes('Server configuration error')) {
      statusCode = 503; // Service Unavailable for configuration issues
      errorMessage = 'Service temporarily unavailable due to configuration issues';
    }
    
    console.error(`[EDGE_SUBMIT_ANSWER] Returning ${statusCode} status with message: ${errorMessage}`);
    
    return new Response(JSON.stringify({ 
      error: errorMessage, 
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    })
  }
})
